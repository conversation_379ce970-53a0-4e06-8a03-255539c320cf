from django.db import models
from audio_processing.models import Transcript

class IRAR<PERSON>ort(models.Model):
    transcript = models.OneToOneField(Transcript, on_delete=models.CASCADE, related_name='ira_report')
    report_text = models.TextField()
    summary = models.TextField()
    sentiment_score = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    report_file = models.FileField(upload_to='reports/', null=True, blank=True)
    
    # Additional analysis fields
    customer_reason = models.TextField(blank=True, null=True)
    agent_action = models.TextField(blank=True, null=True)
    customer_intent = models.TextField(blank=True, null=True)
    quality_score = models.FloatField(null=True, blank=True)  # 1-10 scale
    tokens_processed = models.IntegerField(default=0)
    
    def __str__(self):
        return f"IRA Report for Transcript {self.transcript.id}"
    
    class Meta:
        ordering = ['-created_at']

