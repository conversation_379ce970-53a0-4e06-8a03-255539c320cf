#!/usr/bin/env python3
"""
Test script to demonstrate the email and phone number correction functionality
"""

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'integrated_project.settings')
django.setup()

from pii_extraction.enhanced_services import EnhancedTranscriptCleaner

def test_email_corrections():
    """Test email correction functionality"""
    print("=== Testing Email Corrections ===")
    
    cleaner = EnhancedTranscriptCleaner()
    
    test_cases = [
        "My email is john at the rate gemal dot com",
        "Contact me at jane at the rate yahooo dot com",
        "Send it to bob at the rate gamil dot com",
        "Email address is alice at the rate gnail dot com",
        "Reach out to mike at the rate hotmal dot com",
        "My work email is sarah at the rate outlookt dot com",
        "Use my secure email tom at the rate protonmial dot com",
        "Contact john at dred gmail dot com",  # Test the extended pattern
    ]
    
    for test_case in test_cases:
        corrected = cleaner.correct_email_format(test_case)
        print(f"Original:  {test_case}")
        print(f"Corrected: {corrected}")
        print()

def test_phone_corrections():
    """Test phone number correction functionality"""
    print("=== Testing Phone Number Corrections ===")
    
    cleaner = EnhancedTranscriptCleaner()
    
    test_cases = [
        "My phone number is 12345 67890",
        "Call me at 98765 43210",
        "The number is 55555 12345",
        "Contact 11111 22222 for support",
    ]
    
    for test_case in test_cases:
        corrected = cleaner.correct_phone_number_format(test_case)
        print(f"Original:  {test_case}")
        print(f"Corrected: {corrected}")
        print()

def test_combined_corrections():
    """Test combined email and phone corrections"""
    print("=== Testing Combined Corrections ===")
    
    cleaner = EnhancedTranscriptCleaner()
    
    test_cases = [
        "My email is john at the rate gemal dot com and my phone is 12345 67890",
        "Contact jane at the rate yahooo dot com or call 98765 43210",
        "Email bob at the rate gamil dot com or phone 55555 12345",
    ]
    
    for test_case in test_cases:
        corrected = cleaner.correct_text(test_case)
        print(f"Original:  {test_case}")
        print(f"Corrected: {corrected}")
        print()

def test_full_transcript_cleaning():
    """Test full transcript cleaning with corrections"""
    print("=== Testing Full Transcript Cleaning ===")
    
    cleaner = EnhancedTranscriptCleaner()
    
    test_transcript = """
    [NOISE] Um, so like, my email is john at the rate gemal dot com, you know?
    And uh, my phone number is 12345 67890. Actually, you can also reach me at
    jane at the rate yahooo dot com. *cough* The backup number is 98765 43210.
    """
    
    cleaned = cleaner.clean_transcript(test_transcript)
    print(f"Original transcript:")
    print(test_transcript)
    print(f"\nCleaned transcript:")
    print(cleaned)
    print()

if __name__ == "__main__":
    print("Testing Email and Phone Number Correction Functionality")
    print("=" * 60)
    
    test_email_corrections()
    test_phone_corrections()
    test_combined_corrections()
    test_full_transcript_cleaning()
    
    print("All tests completed!")
