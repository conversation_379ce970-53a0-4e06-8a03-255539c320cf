import torch
from pyannote.audio import Pipeline
from django.conf import settings
import os

class EnhancedSpeakerDiarization:
    """
    Enhanced speaker diarization using pyannote.audio
    """
    
    def __init__(self, hf_token=None):
        """Initialize with HuggingFace token for pyannote access"""
        self.hf_token = hf_token or os.getenv('HUGGINGFACE_TOKEN')
        self.pipeline = None
        
        if self.hf_token:
            try:
                self.pipeline = Pipeline.from_pretrained(
                    "pyannote/speaker-diarization-3.1",
                    use_auth_token=self.hf_token
                )
                
                # Use GPU if available
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                self.pipeline = self.pipeline.to(device)
                
            except Exception as e:
                print(f"Warning: Could not initialize pyannote pipeline: {str(e)}")
                self.pipeline = None
    
    def diarize_speakers(self, audio_path):
        """
        Perform speaker diarization on audio file
        Returns speaker segments with timestamps
        """
        if not self.pipeline:
            # Fallback to simple alternating speaker assignment
            return self._fallback_diarization()
        
        try:
            # Perform diarization
            diarization = self.pipeline(audio_path)
            
            # Convert to our format
            speaker_segments = []
            for turn, _, speaker in diarization.itertracks(yield_label=True):
                speaker_segments.append({
                    "speaker": speaker,
                    "start": turn.start,
                    "end": turn.end
                })
            
            return speaker_segments
            
        except Exception as e:
            print(f"Error in speaker diarization: {str(e)}")
            return self._fallback_diarization()
    
    def _fallback_diarization(self):
        """Fallback method when pyannote is not available"""
        return [
            {"speaker": "SPEAKER_00", "start": 0.0, "end": 30.0},
            {"speaker": "SPEAKER_01", "start": 30.0, "end": 60.0},
        ]
    
    def assign_speaker_roles(self, speaker_segments, transcript_segments):
        """
        Assign roles (agent, customer, broker) to speakers based on patterns
        """
        # Count speaking time for each speaker
        speaker_stats = {}
        for segment in speaker_segments:
            speaker = segment["speaker"]
            duration = segment["end"] - segment["start"]
            
            if speaker not in speaker_stats:
                speaker_stats[speaker] = {"duration": 0, "segments": 0}
            
            speaker_stats[speaker]["duration"] += duration
            speaker_stats[speaker]["segments"] += 1
        
        # Sort speakers by speaking time (agent usually speaks more)
        sorted_speakers = sorted(
            speaker_stats.items(), 
            key=lambda x: x[1]["duration"], 
            reverse=True
        )
        
        # Assign roles based on speaking patterns
        role_mapping = {}
        if len(sorted_speakers) >= 1:
            role_mapping[sorted_speakers[0][0]] = "agent"
        if len(sorted_speakers) >= 2:
            role_mapping[sorted_speakers[1][0]] = "customer"
        if len(sorted_speakers) >= 3:
            role_mapping[sorted_speakers[2][0]] = "broker"
        
        # Apply roles to segments
        assigned_segments = []
        for segment in speaker_segments:
            role = role_mapping.get(segment["speaker"], "unknown")
            assigned_segments.append({
                **segment,
                "role": role
            })
        
        return assigned_segments

class TranscriptProcessor:
    """
    Process transcripts with speaker diarization and role assignment
    """
    
    def __init__(self, hf_token=None):
        self.diarizer = EnhancedSpeakerDiarization(hf_token)
    
    def process_transcript_with_speakers(self, audio_path, transcript_segments):
        """
        Process transcript with speaker diarization and role assignment
        """
        # Perform speaker diarization
        speaker_segments = self.diarizer.diarize_speakers(audio_path)
        
        # Assign roles to speakers
        role_assigned_segments = self.diarizer.assign_speaker_roles(
            speaker_segments, transcript_segments
        )
        
        # Match transcript segments with speaker segments
        matched_dialogues = self._match_transcript_with_speakers(
            transcript_segments, role_assigned_segments
        )
        
        return matched_dialogues
    
    def _match_transcript_with_speakers(self, transcript_segments, speaker_segments):
        """
        Match transcript segments with speaker segments based on timestamps
        """
        matched_dialogues = []
        
        for i, transcript_seg in enumerate(transcript_segments):
            # Find the best matching speaker segment
            best_match = None
            best_overlap = 0
            
            for speaker_seg in speaker_segments:
                # Calculate overlap between transcript and speaker segments
                overlap_start = max(transcript_seg['start'], speaker_seg['start'])
                overlap_end = min(transcript_seg['end'], speaker_seg['end'])
                overlap = max(0, overlap_end - overlap_start)
                
                if overlap > best_overlap:
                    best_overlap = overlap
                    best_match = speaker_seg
            
            # Create dialogue entry
            dialogue = {
                'text': transcript_seg['text'],
                'start_time': transcript_seg['start'],
                'end_time': transcript_seg['end'],
                'speaker': best_match['speaker'] if best_match else f"SPEAKER_{i % 2}",
                'role': best_match['role'] if best_match else ("agent" if i % 2 == 0 else "customer")
            }
            
            matched_dialogues.append(dialogue)
        
        return matched_dialogues

