import os
import gspread
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials as OAuthCredentials
from google_auth_oauthlib.flow import InstalledApp<PERSON>low
from datetime import datetime, timed<PERSON>ta
import json
from django.conf import settings
from .models import CalendarEvent, EmailNotification, SMSNotification
from pii_extraction.models import PIIData

class GoogleSheetsService:
    """Service for Google Sheets integration"""
    
    def __init__(self, credentials_file=None, sheet_id=None):
        self.credentials_file = credentials_file or os.getenv('GOOGLE_CREDENTIALS_FILE')
        self.sheet_id = sheet_id or os.getenv('GOOGLE_SHEET_ID', '1gbVR-5Pbge7kgNBN83qsXfurNC2kc5cxDht5-g41cFE')
        
        if not self.credentials_file:
            raise ValueError("Google credentials file is required")
        
        # Set up credentials
        scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        try:
            creds = Credentials.from_service_account_file(self.credentials_file, scopes=scopes)
            self.gc = gspread.authorize(creds)
        except Exception as e:
            print(f"Warning: Could not initialize Google Sheets: {str(e)}")
            self.gc = None
    
    def save_pii_to_sheets(self, pii_data):
        """Save PII data to Google Sheets"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            
            # Prepare row data in correct order: name, email, date, time, location, contact
            row = [
                ", ".join(pii_data.names) if pii_data.names != ["Not Found"] else "",  # name
                ", ".join(pii_data.emails) if pii_data.emails != ["Not Found"] else "",  # email
                ", ".join(pii_data.appointment_dates) if pii_data.appointment_dates != ["Not Found"] else "",  # date
                ", ".join(pii_data.appointment_times) if pii_data.appointment_times != ["Not Found"] else "",  # time
                ", ".join(pii_data.addresses) if pii_data.addresses != ["Not Found"] else "",  # location
                ", ".join(pii_data.phone_numbers) if pii_data.phone_numbers != ["Not Found"] else "",  # contact
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # Timestamp
                "No"  # Processed flag
            ]
            
            # Append row to sheet
            sheet.append_row(row)
            
            # Create GoogleSheetsEntry record
            from pii_extraction.models import GoogleSheetsEntry
            entry = GoogleSheetsEntry.objects.create(
                pii_data=pii_data,
                sheet_id=self.sheet_id,
                row_number=len(sheet.get_all_values())  # Get current row count
            )
            
            return entry
            
        except Exception as e:
            raise Exception(f"Error saving to Google Sheets: {str(e)}")
    
    def get_new_entries(self):
        """Get new entries from Google Sheets that haven't been processed"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            records = sheet.get_all_records()
            
            # Filter unprocessed entries
            new_entries = []
            for i, record in enumerate(records, start=2):  # Start from row 2 (skip header)
                if record.get('processed', '').lower() != 'yes':
                    record['row_number'] = i
                    new_entries.append(record)
            
            return new_entries
            
        except Exception as e:
            raise Exception(f"Error getting new entries: {str(e)}")
    
    def mark_processed(self, row_number):
        """Mark a row as processed"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            # Assuming 'processed' column is the last column
            processed_col = len(sheet.row_values(1))  # Get number of columns from header
            sheet.update_cell(row_number, processed_col, "Yes")
            
        except Exception as e:
            raise Exception(f"Error marking row as processed: {str(e)}")

class GoogleCalendarService:
    """Service for Google Calendar integration"""
    
    SCOPES = ['https://www.googleapis.com/auth/calendar']
    
    def __init__(self, credentials_file=None):
        self.credentials_file = credentials_file or os.getenv('GOOGLE_CREDENTIALS_FILE')
        self.service = None
        
        if self.credentials_file:
            try:
                creds = Credentials.from_service_account_file(self.credentials_file, scopes=self.SCOPES)
                self.service = build('calendar', 'v3', credentials=creds)
            except Exception as e:
                print(f"Warning: Could not initialize Google Calendar: {str(e)}")
    
    def create_calendar(self, name="Appointments"):
        """Create a new calendar"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            calendar = {
                'summary': name,
                'timeZone': 'UTC'
            }
            
            created_calendar = self.service.calendars().insert(body=calendar).execute()
            return created_calendar['id']
            
        except Exception as e:
            raise Exception(f"Error creating calendar: {str(e)}")
    
    def list_calendars(self):
        """List all calendars"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            calendars_result = self.service.calendarList().list().execute()
            calendars = calendars_result.get('items', [])
            return calendars
            
        except Exception as e:
            raise Exception(f"Error listing calendars: {str(e)}")
    
    def create_event(self, calendar_id, name, email, date, time, location=""):
        """Create a calendar event"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            # Parse date and time
            event_datetime = datetime.strptime(f"{date} {time}", "%Y-%m-%d %H:%M")
            end_datetime = event_datetime + timedelta(hours=1)  # Default 1-hour duration
            
            event = {
                'summary': f"Appointment with {name}",
                'location': location,
                'description': f"Appointment scheduled for {name} ({email})",
                'start': {
                    'dateTime': event_datetime.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': end_datetime.isoformat(),
                    'timeZone': 'UTC',
                },
                # Removed attendees to avoid service account permission issues
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'popup', 'minutes': 10},       # 10 minutes before
                    ],
                },
            }
            
            created_event = self.service.events().insert(
                calendarId=calendar_id, 
                body=event
            ).execute()
            
            return created_event.get('htmlLink')
            
        except Exception as e:
            raise Exception(f"Error creating event: {str(e)}")
    
    def share_calendar(self, calendar_id, email, role='reader'):
        """Share calendar with a user"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            rule = {
                'scope': {
                    'type': 'user',
                    'value': email,
                },
                'role': role
            }
            
            created_rule = self.service.acl().insert(
                calendarId=calendar_id, 
                body=rule
            ).execute()
            
            return created_rule
            
        except Exception as e:
            raise Exception(f"Error sharing calendar: {str(e)}")

class EmailService:
    """Service for sending email notifications"""
    
    def __init__(self):
        # This is a placeholder - in production, you'd use a service like SendGrid, SES, etc.
        pass
    
    def send_email(self, to_email, subject, body):
        """Send email notification"""
        try:
            # Placeholder implementation
            # In production, integrate with actual email service
            print(f"Sending email to {to_email}")
            print(f"Subject: {subject}")
            print(f"Body: {body}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error sending email: {str(e)}")

class SMSService:
    """Service for sending SMS notifications"""
    
    def __init__(self):
        # This is a placeholder - in production, you'd use a service like Twilio
        pass
    
    def send_sms(self, phone_number, message):
        """Send SMS notification"""
        try:
            # Placeholder implementation
            # In production, integrate with actual SMS service
            print(f"Sending SMS to {phone_number}")
            print(f"Message: {message}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error sending SMS: {str(e)}")

class CalendarSchedulerService:
    """Main service for calendar scheduling workflow"""
    
    def __init__(self, credentials_file=None, sheet_id=None):
        self.sheets_service = GoogleSheetsService(credentials_file, sheet_id)
        self.calendar_service = GoogleCalendarService(credentials_file)
        self.email_service = EmailService()
        self.sms_service = SMSService()
    
    def create_event_from_pii(self, pii_data_id):
        """Create calendar event from PII data"""
        try:
            pii_data = PIIData.objects.get(id=pii_data_id)
            
            # Extract required information with better filtering
            names = [n for n in pii_data.names if n and n != "Not Found"]
            emails = [e for e in pii_data.emails if e and e != "Not Found"]
            phones = [p for p in pii_data.phone_numbers if p and p != "Not Found"]
            dates = [d for d in pii_data.appointment_dates if d and d != "Not Found"]
            times = [t for t in pii_data.appointment_times if t and t != "Not Found" and t != "None" and t.strip() != "None"]
            addresses = [a for a in pii_data.addresses if a and a != "Not Found"]
            
            # Check for minimum required data (name and email are essential)
            if not names or not emails:
                raise Exception("Insufficient data to create calendar event: Name and email are required")

            # Provide defaults for missing optional data
            if not dates:
                dates = [datetime.now().strftime("%Y-%m-%d")]  # Default to today
            if not times:
                times = ["10:00"]  # Default to 10:00 AM
            
            # Use first available values with better processing
            name = names[0]
            email = emails[0]
            phone = phones[0] if phones else ""

            # Process date - convert various formats to YYYY-MM-DD
            raw_date = dates[0] if dates else datetime.now().strftime("%Y-%m-%d")
            date = self._parse_date(raw_date)

            # Process time - convert various formats to HH:MM
            raw_time = times[0] if times else "10:00"
            print(f"🔍 Debug: raw_time = '{raw_time}', times = {times}")
            time = self._parse_time(raw_time)
            print(f"🔍 Debug: parsed time = '{time}'")

            location = addresses[0] if addresses else ""
            
            # Get or create calendar
            calendars = self.calendar_service.list_calendars()
            calendar_id = None
            
            for calendar in calendars:
                if "Appointments" in calendar["summary"]:
                    calendar_id = calendar["id"]
                    break
            
            if not calendar_id:
                calendar_id = self.calendar_service.create_calendar()
            
            # Create event
            event_link = self.calendar_service.create_event(
                calendar_id, name, email, date, time, location
            )
            
            # Create CalendarEvent record
            calendar_event = CalendarEvent.objects.create(
                pii_data=pii_data,
                calendar_id=calendar_id,
                event_id="",  # Would be populated with actual event ID
                name=name,
                email=email,
                phone=phone,
                date=datetime.strptime(date, "%Y-%m-%d").date(),
                time=datetime.strptime(time, "%H:%M").time(),
                location=location,
                event_link=event_link
            )
            
            return calendar_event
            
        except PIIData.DoesNotExist:
            raise Exception(f"PII data with id {pii_data_id} not found")
        except Exception as e:
            raise Exception(f"Error creating calendar event: {str(e)}")

    def create_event_from_sheet_entry(self, sheet_entry_id):
        """Create calendar event directly from Google Sheets entry (reads live data from sheets)"""
        try:
            from pii_extraction.models import GoogleSheetsEntry

            # Get the sheet entry to get row number and sheet info
            sheet_entry = GoogleSheetsEntry.objects.get(id=sheet_entry_id)
            pii_data = sheet_entry.pii_data
            row_number = sheet_entry.row_number

            print(f"🔍 Reading live data from Google Sheets row {row_number}")

            # Read current data directly from Google Sheets
            if not self.sheets_service.gc:
                raise Exception("Google Sheets not initialized")

            sheet = self.sheets_service.gc.open_by_key(self.sheets_service.sheet_id).sheet1

            # Get the specific row data (row_number is 1-based)
            row_values = sheet.row_values(row_number)

            print(f"🔍 Raw row data: {row_values}")

            # Parse row data based on column order: name, email, date, time, location, contact, timestamp, processed
            if len(row_values) < 6:
                raise Exception(f"Insufficient data in sheet row {row_number}. Expected at least 6 columns.")

            # Extract data from sheet row (live data)
            sheet_name = row_values[0].strip() if len(row_values) > 0 and row_values[0] else ""
            sheet_email = row_values[1].strip() if len(row_values) > 1 and row_values[1] else ""
            sheet_date = row_values[2].strip() if len(row_values) > 2 and row_values[2] else ""
            sheet_time = row_values[3].strip() if len(row_values) > 3 and row_values[3] else ""
            sheet_location = row_values[4].strip() if len(row_values) > 4 and row_values[4] else ""
            sheet_contact = row_values[5].strip() if len(row_values) > 5 and row_values[5] else ""

            print(f"🔍 Parsed sheet data:")
            print(f"   Name: '{sheet_name}'")
            print(f"   Email: '{sheet_email}'")
            print(f"   Date: '{sheet_date}'")
            print(f"   Time: '{sheet_time}'")
            print(f"   Location: '{sheet_location}'")
            print(f"   Contact: '{sheet_contact}'")

            # Validate minimum required data (name and email are essential)
            if not sheet_name or not sheet_email:
                raise Exception("Insufficient data: Name and email are required for calendar event creation")

            # Use sheet data with defaults for missing values
            name = sheet_name
            email = sheet_email
            phone = sheet_contact
            location = sheet_location

            # Process date and time with defaults
            if sheet_date:
                date = self._parse_date(sheet_date)
            else:
                date = datetime.now().strftime("%Y-%m-%d")

            if sheet_time:
                time = self._parse_time(sheet_time)
            else:
                time = "10:00"

            print(f"🔍 Final processed data:")
            print(f"   Name: '{name}'")
            print(f"   Email: '{email}'")
            print(f"   Date: '{date}'")
            print(f"   Time: '{time}'")
            print(f"   Location: '{location}'")
            print(f"   Phone: '{phone}'")

            # Get or create calendar
            calendars = self.calendar_service.list_calendars()
            calendar_id = None

            for calendar in calendars:
                if "Appointments" in calendar["summary"]:
                    calendar_id = calendar["id"]
                    break

            if not calendar_id:
                calendar_id = self.calendar_service.create_calendar()

            # Create event using live sheet data
            event_link = self.calendar_service.create_event(
                calendar_id, name, email, date, time, location
            )

            # Create CalendarEvent record with live sheet data
            calendar_event = CalendarEvent.objects.create(
                pii_data=pii_data,
                calendar_id=calendar_id,
                event_id="",  # Would be populated with actual event ID
                name=name,
                email=email,  # This will be the updated email from sheets
                phone=phone,  # This will be the updated phone from sheets
                date=datetime.strptime(date, "%Y-%m-%d").date(),
                time=datetime.strptime(time, "%H:%M").time(),
                location=location,  # This will be the updated location from sheets
                event_link=event_link
            )

            # Mark sheet entry as processed
            sheet_entry.processed = True
            sheet_entry.save()

            print(f"✅ Calendar event created with live sheet data:")
            print(f"   Event ID: {calendar_event.id}")
            print(f"   Using Email: {email}")
            print(f"   Using Phone: {phone}")
            print(f"   Event Link: {event_link}")

            return calendar_event

        except GoogleSheetsEntry.DoesNotExist:
            raise Exception(f"Google Sheets entry with id {sheet_entry_id} not found")
        except Exception as e:
            raise Exception(f"Error creating calendar event from sheet entry: {str(e)}")

    def send_notifications(self, calendar_event_id):
        """Send email and SMS notifications for an event"""
        try:
            calendar_event = CalendarEvent.objects.get(id=calendar_event_id)
            
            # Send email notification
            email_body = f"""
Hello {calendar_event.name},

Your appointment is scheduled on {calendar_event.date} at {calendar_event.time}.

Event Link: {calendar_event.event_link}

Location: {calendar_event.location}

Best regards.
            """
            
            email_sent = self.email_service.send_email(
                calendar_event.email,
                "Appointment Confirmation",
                email_body
            )
            
            # Create email notification record
            EmailNotification.objects.create(
                calendar_event=calendar_event,
                status="sent" if email_sent else "failed"
            )
            
            # Send SMS notification if phone number is available
            if calendar_event.phone:
                sms_message = f"Hello {calendar_event.name}, your appointment is on {calendar_event.date} at {calendar_event.time}. Check your email for details."
                
                sms_sent = self.sms_service.send_sms(
                    calendar_event.phone,
                    sms_message
                )
                
                # Create SMS notification record
                SMSNotification.objects.create(
                    calendar_event=calendar_event,
                    status="sent" if sms_sent else "failed"
                )
            
            return {
                "email_sent": email_sent,
                "sms_sent": calendar_event.phone is not None
            }
            
        except CalendarEvent.DoesNotExist:
            raise Exception(f"Calendar event with id {calendar_event_id} not found")
        except Exception as e:
            raise Exception(f"Error sending notifications: {str(e)}")
    
    def process_new_sheet_entries(self):
        """Process new entries from Google Sheets and create calendar events"""
        try:
            new_entries = self.sheets_service.get_new_entries()
            processed_events = []
            
            for entry in new_entries:
                try:
                    # Create calendar event from sheet entry
                    event_link = self.calendar_service.create_event(
                        calendar_id="primary",  # Use primary calendar
                        name=entry.get('name', ''),
                        email=entry.get('email', ''),
                        date=entry.get('date', ''),
                        time=entry.get('time', ''),
                        location=entry.get('location', '')
                    )
                    
                    # Send notifications
                    email_body = f"Hello {entry.get('name', '')},\n\nYour appointment is scheduled on {entry.get('date', '')} at {entry.get('time', '')}.\n\nEvent Link: {event_link}\n\nBest regards."
                    
                    self.email_service.send_email(
                        entry.get('email', ''),
                        "Appointment Confirmation",
                        email_body
                    )
                    
                    # Mark as processed
                    self.sheets_service.mark_processed(entry['row_number'])
                    
                    processed_events.append({
                        'name': entry.get('name', ''),
                        'event_link': event_link
                    })
                    
                except Exception as e:
                    print(f"Error processing entry {entry}: {str(e)}")
                    continue
            
            return processed_events
            
        except Exception as e:
            raise Exception(f"Error processing sheet entries: {str(e)}")

    def _parse_date(self, date_str):
        """Parse various date formats to YYYY-MM-DD"""
        if not date_str:
            return datetime.now().strftime("%Y-%m-%d")

        # If already in YYYY-MM-DD format
        if len(date_str) == 10 and date_str.count('-') == 2:
            try:
                datetime.strptime(date_str, "%Y-%m-%d")
                return date_str
            except:
                pass

        # Try to parse common formats
        date_formats = [
            "%Y-%m-%d",
            "%d/%m/%Y",
            "%m/%d/%Y",
            "%d-%m-%Y",
            "%m-%d-%Y"
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                return parsed_date.strftime("%Y-%m-%d")
            except:
                continue

        # Handle text dates like "5th December", "December 5th"
        try:
            import re
            # Extract day and month from text
            day_match = re.search(r'(\d{1,2})', date_str)
            month_match = re.search(r'(January|February|March|April|May|June|July|August|September|October|November|December)', date_str, re.IGNORECASE)

            if day_match and month_match:
                day = int(day_match.group(1))
                month_name = month_match.group(1).lower()

                month_map = {
                    'january': 1, 'february': 2, 'march': 3, 'april': 4,
                    'may': 5, 'june': 6, 'july': 7, 'august': 8,
                    'september': 9, 'october': 10, 'november': 11, 'december': 12
                }

                month = month_map.get(month_name)
                if month:
                    # Use current year or next year if date has passed
                    current_year = datetime.now().year
                    try:
                        parsed_date = datetime(current_year, month, day)
                        if parsed_date < datetime.now():
                            parsed_date = datetime(current_year + 1, month, day)
                        return parsed_date.strftime("%Y-%m-%d")
                    except:
                        pass
        except:
            pass

        # Default to today if parsing fails
        return datetime.now().strftime("%Y-%m-%d")

    def _parse_time(self, time_str):
        """Parse various time formats to HH:MM"""
        if not time_str or time_str == "None":
            return "10:00"

        # If already in HH:MM format
        if len(time_str) == 5 and time_str.count(':') == 1:
            try:
                datetime.strptime(time_str, "%H:%M")
                return time_str
            except:
                pass

        # Try to parse common formats
        time_formats = [
            "%H:%M",
            "%I:%M %p",  # 2:30 PM
            "%I:%M%p",   # 2:30PM
            "%H.%M",     # 14.30
            "%I.%M %p"   # 2.30 PM
        ]

        for fmt in time_formats:
            try:
                parsed_time = datetime.strptime(time_str, fmt)
                return parsed_time.strftime("%H:%M")
            except:
                continue

        # Handle text times
        try:
            import re
            # Convert AM/PM times
            if 'pm' in time_str.lower() or 'p.m' in time_str.lower():
                hour_match = re.search(r'(\d{1,2})', time_str)
                if hour_match:
                    hour = int(hour_match.group(1))
                    if hour != 12:
                        hour += 12
                    return f"{hour:02d}:00"
            elif 'am' in time_str.lower() or 'a.m' in time_str.lower():
                hour_match = re.search(r'(\d{1,2})', time_str)
                if hour_match:
                    hour = int(hour_match.group(1))
                    if hour == 12:
                        hour = 0
                    return f"{hour:02d}:00"
        except:
            pass

        # Default to 10:00 AM if parsing fails
        return "10:00"

