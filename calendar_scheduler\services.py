import os
import gspread
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials as OAuthCredentials
from google_auth_oauthlib.flow import InstalledApp<PERSON>low
from datetime import datetime, timed<PERSON>ta
import json
from django.conf import settings
from .models import CalendarEvent, EmailNotification, SMSNotification
from pii_extraction.models import PIIData

class GoogleSheetsService:
    """Service for Google Sheets integration"""
    
    def __init__(self, credentials_file=None, sheet_id=None):
        self.credentials_file = credentials_file or os.getenv('GOOGLE_CREDENTIALS_FILE')
        self.sheet_id = sheet_id or os.getenv('GOOGLE_SHEET_ID', '1gbVR-5Pbge7kgNBN83qsXfurNC2kc5cxDht5-g41cFE')
        
        if not self.credentials_file:
            raise ValueError("Google credentials file is required")
        
        # Set up credentials
        scopes = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]
        
        try:
            creds = Credentials.from_service_account_file(self.credentials_file, scopes=scopes)
            self.gc = gspread.authorize(creds)
        except Exception as e:
            print(f"Warning: Could not initialize Google Sheets: {str(e)}")
            self.gc = None
    
    def save_pii_to_sheets(self, pii_data):
        """Save PII data to Google Sheets"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            
            # Prepare row data
            row = [
                ", ".join(pii_data.names) if pii_data.names != ["Not Found"] else "",
                ", ".join(pii_data.emails) if pii_data.emails != ["Not Found"] else "",
                ", ".join(pii_data.phone_numbers) if pii_data.phone_numbers != ["Not Found"] else "",
                ", ".join(pii_data.appointment_dates) if pii_data.appointment_dates != ["Not Found"] else "",
                ", ".join(pii_data.appointment_times) if pii_data.appointment_times != ["Not Found"] else "",
                ", ".join(pii_data.addresses) if pii_data.addresses != ["Not Found"] else "",
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),  # Timestamp
                "No"  # Processed flag
            ]
            
            # Append row to sheet
            sheet.append_row(row)
            
            # Create GoogleSheetsEntry record
            from pii_extraction.models import GoogleSheetsEntry
            entry = GoogleSheetsEntry.objects.create(
                pii_data=pii_data,
                sheet_id=self.sheet_id,
                row_number=len(sheet.get_all_values())  # Get current row count
            )
            
            return entry
            
        except Exception as e:
            raise Exception(f"Error saving to Google Sheets: {str(e)}")
    
    def get_new_entries(self):
        """Get new entries from Google Sheets that haven't been processed"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            records = sheet.get_all_records()
            
            # Filter unprocessed entries
            new_entries = []
            for i, record in enumerate(records, start=2):  # Start from row 2 (skip header)
                if record.get('processed', '').lower() != 'yes':
                    record['row_number'] = i
                    new_entries.append(record)
            
            return new_entries
            
        except Exception as e:
            raise Exception(f"Error getting new entries: {str(e)}")
    
    def mark_processed(self, row_number):
        """Mark a row as processed"""
        if not self.gc:
            raise Exception("Google Sheets not initialized")
        
        try:
            sheet = self.gc.open_by_key(self.sheet_id).sheet1
            # Assuming 'processed' column is the last column
            processed_col = len(sheet.row_values(1))  # Get number of columns from header
            sheet.update_cell(row_number, processed_col, "Yes")
            
        except Exception as e:
            raise Exception(f"Error marking row as processed: {str(e)}")

class GoogleCalendarService:
    """Service for Google Calendar integration"""
    
    SCOPES = ['https://www.googleapis.com/auth/calendar']
    
    def __init__(self, credentials_file=None):
        self.credentials_file = credentials_file or os.getenv('GOOGLE_CREDENTIALS_FILE')
        self.service = None
        
        if self.credentials_file:
            try:
                creds = Credentials.from_service_account_file(self.credentials_file, scopes=self.SCOPES)
                self.service = build('calendar', 'v3', credentials=creds)
            except Exception as e:
                print(f"Warning: Could not initialize Google Calendar: {str(e)}")
    
    def create_calendar(self, name="Appointments"):
        """Create a new calendar"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            calendar = {
                'summary': name,
                'timeZone': 'UTC'
            }
            
            created_calendar = self.service.calendars().insert(body=calendar).execute()
            return created_calendar['id']
            
        except Exception as e:
            raise Exception(f"Error creating calendar: {str(e)}")
    
    def list_calendars(self):
        """List all calendars"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            calendars_result = self.service.calendarList().list().execute()
            calendars = calendars_result.get('items', [])
            return calendars
            
        except Exception as e:
            raise Exception(f"Error listing calendars: {str(e)}")
    
    def create_event(self, calendar_id, name, email, date, time, location=""):
        """Create a calendar event"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            # Parse date and time
            event_datetime = datetime.strptime(f"{date} {time}", "%Y-%m-%d %H:%M")
            end_datetime = event_datetime + timedelta(hours=1)  # Default 1-hour duration
            
            event = {
                'summary': f"Appointment with {name}",
                'location': location,
                'description': f"Appointment scheduled for {name} ({email})",
                'start': {
                    'dateTime': event_datetime.isoformat(),
                    'timeZone': 'UTC',
                },
                'end': {
                    'dateTime': end_datetime.isoformat(),
                    'timeZone': 'UTC',
                },
                'attendees': [
                    {'email': email},
                ],
                'reminders': {
                    'useDefault': False,
                    'overrides': [
                        {'method': 'email', 'minutes': 24 * 60},  # 1 day before
                        {'method': 'popup', 'minutes': 10},       # 10 minutes before
                    ],
                },
            }
            
            created_event = self.service.events().insert(
                calendarId=calendar_id, 
                body=event
            ).execute()
            
            return created_event.get('htmlLink')
            
        except Exception as e:
            raise Exception(f"Error creating event: {str(e)}")
    
    def share_calendar(self, calendar_id, email, role='reader'):
        """Share calendar with a user"""
        if not self.service:
            raise Exception("Google Calendar not initialized")
        
        try:
            rule = {
                'scope': {
                    'type': 'user',
                    'value': email,
                },
                'role': role
            }
            
            created_rule = self.service.acl().insert(
                calendarId=calendar_id, 
                body=rule
            ).execute()
            
            return created_rule
            
        except Exception as e:
            raise Exception(f"Error sharing calendar: {str(e)}")

class EmailService:
    """Service for sending email notifications"""
    
    def __init__(self):
        # This is a placeholder - in production, you'd use a service like SendGrid, SES, etc.
        pass
    
    def send_email(self, to_email, subject, body):
        """Send email notification"""
        try:
            # Placeholder implementation
            # In production, integrate with actual email service
            print(f"Sending email to {to_email}")
            print(f"Subject: {subject}")
            print(f"Body: {body}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error sending email: {str(e)}")

class SMSService:
    """Service for sending SMS notifications"""
    
    def __init__(self):
        # This is a placeholder - in production, you'd use a service like Twilio
        pass
    
    def send_sms(self, phone_number, message):
        """Send SMS notification"""
        try:
            # Placeholder implementation
            # In production, integrate with actual SMS service
            print(f"Sending SMS to {phone_number}")
            print(f"Message: {message}")
            
            return True
            
        except Exception as e:
            raise Exception(f"Error sending SMS: {str(e)}")

class CalendarSchedulerService:
    """Main service for calendar scheduling workflow"""
    
    def __init__(self, credentials_file=None, sheet_id=None):
        self.sheets_service = GoogleSheetsService(credentials_file, sheet_id)
        self.calendar_service = GoogleCalendarService(credentials_file)
        self.email_service = EmailService()
        self.sms_service = SMSService()
    
    def create_event_from_pii(self, pii_data_id):
        """Create calendar event from PII data"""
        try:
            pii_data = PIIData.objects.get(id=pii_data_id)
            
            # Extract required information
            names = pii_data.names if pii_data.names != ["Not Found"] else []
            emails = pii_data.emails if pii_data.emails != ["Not Found"] else []
            phones = pii_data.phone_numbers if pii_data.phone_numbers != ["Not Found"] else []
            dates = pii_data.appointment_dates if pii_data.appointment_dates != ["Not Found"] else []
            times = pii_data.appointment_times if pii_data.appointment_times != ["Not Found"] else []
            addresses = pii_data.addresses if pii_data.addresses != ["Not Found"] else []
            
            if not names or not emails or not dates or not times:
                raise Exception("Insufficient data to create calendar event")
            
            # Use first available values
            name = names[0]
            email = emails[0]
            phone = phones[0] if phones else ""
            date = dates[0]
            time = times[0]
            location = addresses[0] if addresses else ""
            
            # Get or create calendar
            calendars = self.calendar_service.list_calendars()
            calendar_id = None
            
            for calendar in calendars:
                if "Appointments" in calendar["summary"]:
                    calendar_id = calendar["id"]
                    break
            
            if not calendar_id:
                calendar_id = self.calendar_service.create_calendar()
            
            # Create event
            event_link = self.calendar_service.create_event(
                calendar_id, name, email, date, time, location
            )
            
            # Create CalendarEvent record
            calendar_event = CalendarEvent.objects.create(
                pii_data=pii_data,
                calendar_id=calendar_id,
                event_id="",  # Would be populated with actual event ID
                name=name,
                email=email,
                phone=phone,
                date=datetime.strptime(date, "%Y-%m-%d").date(),
                time=datetime.strptime(time, "%H:%M").time(),
                location=location,
                event_link=event_link
            )
            
            return calendar_event
            
        except PIIData.DoesNotExist:
            raise Exception(f"PII data with id {pii_data_id} not found")
        except Exception as e:
            raise Exception(f"Error creating calendar event: {str(e)}")
    
    def send_notifications(self, calendar_event_id):
        """Send email and SMS notifications for an event"""
        try:
            calendar_event = CalendarEvent.objects.get(id=calendar_event_id)
            
            # Send email notification
            email_body = f"""
Hello {calendar_event.name},

Your appointment is scheduled on {calendar_event.date} at {calendar_event.time}.

Event Link: {calendar_event.event_link}

Location: {calendar_event.location}

Best regards.
            """
            
            email_sent = self.email_service.send_email(
                calendar_event.email,
                "Appointment Confirmation",
                email_body
            )
            
            # Create email notification record
            EmailNotification.objects.create(
                calendar_event=calendar_event,
                status="sent" if email_sent else "failed"
            )
            
            # Send SMS notification if phone number is available
            if calendar_event.phone:
                sms_message = f"Hello {calendar_event.name}, your appointment is on {calendar_event.date} at {calendar_event.time}. Check your email for details."
                
                sms_sent = self.sms_service.send_sms(
                    calendar_event.phone,
                    sms_message
                )
                
                # Create SMS notification record
                SMSNotification.objects.create(
                    calendar_event=calendar_event,
                    status="sent" if sms_sent else "failed"
                )
            
            return {
                "email_sent": email_sent,
                "sms_sent": calendar_event.phone is not None
            }
            
        except CalendarEvent.DoesNotExist:
            raise Exception(f"Calendar event with id {calendar_event_id} not found")
        except Exception as e:
            raise Exception(f"Error sending notifications: {str(e)}")
    
    def process_new_sheet_entries(self):
        """Process new entries from Google Sheets and create calendar events"""
        try:
            new_entries = self.sheets_service.get_new_entries()
            processed_events = []
            
            for entry in new_entries:
                try:
                    # Create calendar event from sheet entry
                    event_link = self.calendar_service.create_event(
                        calendar_id="primary",  # Use primary calendar
                        name=entry.get('name', ''),
                        email=entry.get('email', ''),
                        date=entry.get('date', ''),
                        time=entry.get('time', ''),
                        location=entry.get('location', '')
                    )
                    
                    # Send notifications
                    email_body = f"Hello {entry.get('name', '')},\n\nYour appointment is scheduled on {entry.get('date', '')} at {entry.get('time', '')}.\n\nEvent Link: {event_link}\n\nBest regards."
                    
                    self.email_service.send_email(
                        entry.get('email', ''),
                        "Appointment Confirmation",
                        email_body
                    )
                    
                    # Mark as processed
                    self.sheets_service.mark_processed(entry['row_number'])
                    
                    processed_events.append({
                        'name': entry.get('name', ''),
                        'event_link': event_link
                    })
                    
                except Exception as e:
                    print(f"Error processing entry {entry}: {str(e)}")
                    continue
            
            return processed_events
            
        except Exception as e:
            raise Exception(f"Error processing sheet entries: {str(e)}")

