from django.db import models
from pii_extraction.models import PIIData

class CalendarEvent(models.Model):
    pii_data = models.ForeignKey(PIIData, on_delete=models.CASCADE, related_name='calendar_events')
    calendar_id = models.CharField(max_length=255)
    event_id = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=20, blank=True, null=True)
    date = models.DateField()
    time = models.TimeField()
    location = models.CharField(max_length=255, blank=True, null=True)
    event_link = models.URLField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Calendar Event for {self.name} on {self.date} at {self.time}"

class EmailNotification(models.Model):
    calendar_event = models.ForeignKey(CalendarEvent, on_delete=models.CASCADE, related_name='email_notifications')
    sent_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50)
    
    def __str__(self):
        return f"Email Notification for Event {self.calendar_event.id}"

class SMSNotification(models.Model):
    calendar_event = models.ForeignKey(CalendarEvent, on_delete=models.CASCADE, related_name='sms_notifications')
    sent_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50)
    
    def __str__(self):
        return f"SMS Notification for Event {self.calendar_event.id}"

