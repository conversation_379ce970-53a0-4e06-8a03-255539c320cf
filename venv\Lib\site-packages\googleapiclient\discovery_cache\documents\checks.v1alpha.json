{"basePath": "", "baseUrl": "https://checks.googleapis.com/", "batchPath": "batch", "canonicalName": "Checks Service", "description": "The Checks API contains powerful and easy-to-use privacy and compliance APIs that interact with the Checks product and its underlying technology.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/checks", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "checks:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://checks.mtls.googleapis.com/", "name": "checks", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"apps": {"methods": {"get": {"description": "Gets an app.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}", "httpMethod": "GET", "id": "checks.accounts.apps.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of the app. Example: `accounts/123/apps/456`", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleChecksAccountV1alphaApp"}}, "list": {"description": "Lists the apps under the given account.", "flatPath": "v1alpha/accounts/{accountsId}/apps", "httpMethod": "GET", "id": "checks.accounts.apps.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of results to return. The server may further constrain the maximum number of results returned in a single page. If unspecified, the server will decide the number of results to be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous `ListApps` call. Provide this to retrieve the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent account. Example: `accounts/123`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/apps", "response": {"$ref": "GoogleChecksAccountV1alphaListAppsResponse"}}}, "resources": {"operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "checks.accounts.apps.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "checks.accounts.apps.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/operations/{operationsId}", "httpMethod": "GET", "id": "checks.accounts.apps.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Operation"}}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/operations", "httpMethod": "GET", "id": "checks.accounts.apps.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}}, "wait": {"description": "Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/operations/{operationsId}:wait", "httpMethod": "POST", "id": "checks.accounts.apps.operations.wait", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to wait on.", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:wait", "request": {"$ref": "WaitOperationRequest"}, "response": {"$ref": "Operation"}}}}, "reports": {"methods": {"get": {"description": "Gets a report. By default, only the name and results_uri fields are returned. You can include other fields by listing them in the `fields` URL query parameter. For example, `?fields=name,checks` will return the name and checks fields.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/reports/{reportsId}", "httpMethod": "GET", "id": "checks.accounts.apps.reports.get", "parameterOrder": ["name"], "parameters": {"checksFilter": {"description": "Optional. An [AIP-160](https://google.aip.dev/160) filter string to filter checks within the report. Only checks that match the filter string are included in the response. Example: `state = FAILED`", "location": "query", "type": "string"}, "name": {"description": "Required. Resource name of the report. Example: `accounts/123/apps/456/reports/789`", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+/reports/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleChecksReportV1alphaReport"}}, "list": {"description": "Lists reports for the specified app. By default, only the name and results_uri fields are returned. You can include other fields by listing them in the `fields` URL query parameter. For example, `?fields=reports(name,checks)` will return the name and checks fields.", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/reports", "httpMethod": "GET", "id": "checks.accounts.apps.reports.list", "parameterOrder": ["parent"], "parameters": {"checksFilter": {"description": "Optional. An [AIP-160](https://google.aip.dev/160) filter string to filter checks within reports. Only checks that match the filter string are included in the response. Example: `state = FAILED`", "location": "query", "type": "string"}, "filter": {"description": "Optional. An [AIP-160](https://google.aip.dev/160) filter string to filter reports. Example: `appBundle.releaseType = PRE_RELEASE`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of reports to return. If unspecified, at most 10 reports will be returned. The maximum value is 50; values above 50 will be coerced to 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from a previous `ListReports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListReports` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Resource name of the app. Example: `accounts/123/apps/456`", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/reports", "response": {"$ref": "GoogleChecksReportV1alphaListReportsResponse"}}}}}}}}, "media": {"methods": {"upload": {"description": "Analyzes the uploaded app bundle and returns a google.longrunning.Operation containing the generated Report. ## Example (upload only) Send a regular POST request with the header `X-Goog-Upload-Protocol: raw`. ``` POST https://checks.googleapis.com/upload/v1alpha/{parent=accounts/*/apps/*}/reports:analyzeUpload HTTP/1.1 X-Goog-Upload-Protocol: raw Content-Length: Content-Type: application/octet-stream ``` ## Example (upload with metadata) Send a multipart POST request where the first body part contains the metadata JSON and the second body part contains the binary upload. Include the header `X-Goog-Upload-Protocol: multipart`. ``` POST https://checks.googleapis.com/upload/v1alpha/{parent=accounts/*/apps/*}/reports:analyzeUpload HTTP/1.1 X-Goog-Upload-Protocol: multipart Content-Length: ? Content-Type: multipart/related; boundary=BOUNDARY --BOUNDARY Content-Type: application/json {\"code_reference_id\":\"db5bcc20f94055fb5bc08cbb9b0e7a5530308786\"} --BOUNDARY --BOUNDARY-- ``` *Note:* Metadata-only requests are not supported. ", "flatPath": "v1alpha/accounts/{accountsId}/apps/{appsId}/reports:analyzeUpload", "httpMethod": "POST", "id": "checks.media.upload", "mediaUpload": {"accept": ["*/*"], "maxSize": "***********", "protocols": {"simple": {"multipart": true, "path": "/upload/v1alpha/{+parent}/reports:analyzeUpload"}}}, "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. Resource name of the app. Example: `accounts/123/apps/456`", "location": "path", "pattern": "^accounts/[^/]+/apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/reports:analyzeUpload", "request": {"$ref": "GoogleChecksReportV1alphaAnalyzeUploadRequest"}, "response": {"$ref": "Operation"}, "supportsMediaUpload": true}}}}, "revision": "********", "rootUrl": "https://checks.googleapis.com/", "schemas": {"CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "GoogleChecksAccountV1alphaApp": {"description": "Represents an app in Checks.", "id": "GoogleChecksAccountV1alphaApp", "properties": {"name": {"description": "The resource name of the app. Example: `accounts/123/apps/456`", "type": "string"}, "title": {"description": "The app's title.", "type": "string"}}, "type": "object"}, "GoogleChecksAccountV1alphaListAppsResponse": {"description": "The response message for AccountService.ListApps.", "id": "GoogleChecksAccountV1alphaListAppsResponse", "properties": {"apps": {"description": "The apps.", "items": {"$ref": "GoogleChecksAccountV1alphaApp"}, "type": "array"}, "nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaAnalyzeUploadRequest": {"description": "The request message for ReportService.AnalyzeUpload.", "id": "GoogleChecksReportV1alphaAnalyzeUploadRequest", "properties": {"appBinaryFileType": {"description": "Optional. The type of the uploaded app binary. If not provided, the server assumes APK file for Android and IPA file for iOS.", "enum": ["APP_BINARY_FILE_TYPE_UNSPECIFIED", "ANDROID_APK", "ANDROID_AAB", "IOS_IPA"], "enumDescriptions": ["Not specified.", ".apk file type.", ".aab (app bundle) file type.", ".ipa file type."], "type": "string"}, "codeReferenceId": {"description": "Optional. Git commit hash or changelist number associated with the upload.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaAppBundle": {"description": "Information about the analyzed app bundle.", "id": "GoogleChecksReportV1alphaAppBundle", "properties": {"bundleId": {"description": "Unique id of the bundle. For example: \"com.google.Gmail\".", "type": "string"}, "codeReferenceId": {"description": "Git commit hash or changelist number associated with the release.", "type": "string"}, "releaseType": {"description": "Identifies the type of release.", "enum": ["APP_BUNDLE_RELEASE_TYPE_UNSPECIFIED", "PUBLIC", "PRE_RELEASE"], "enumDescriptions": ["Not specified.", "Published production bundle.", "Pre-release bundle."], "type": "string"}, "version": {"description": "The user-visible version of the bundle such as the Android `versionName` or iOS `CFBundleShortVersionString`. For example: \"7.21.1\".", "type": "string"}, "versionId": {"description": "The version used throughout the operating system and store to identify the build such as the Android `versionCode` or iOS `CFBundleVersion`.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaCheck": {"description": "A check that was run on your app.", "id": "GoogleChecksReportV1alphaCheck", "properties": {"citations": {"description": "Regulations and policies that serve as the legal basis for the check.", "items": {"$ref": "GoogleChecksReportV1alphaCheckCitation"}, "type": "array"}, "evidence": {"$ref": "GoogleChecksReportV1alphaCheckEvidence", "description": "Evidence that substantiates the check result."}, "regionCodes": {"description": "Regions that are impacted by the check. For more info, see https://google.aip.dev/143#countries-and-regions.", "items": {"type": "string"}, "type": "array"}, "severity": {"description": "The urgency or risk level of the check.", "enum": ["CHECK_SEVERITY_UNSPECIFIED", "PRIORITY", "POTENTIAL", "OPPORTUNITY"], "enumDescriptions": ["Not specified.", "Important privacy issue.", "Potential privacy issue.", "Opportunity to improve privacy coverage."], "type": "string"}, "state": {"description": "The result after running the check.", "enum": ["CHECK_STATE_UNSPECIFIED", "PASSED", "FAILED", "UNCHECKED"], "enumDescriptions": ["Not specified.", "The check passed.", "The check failed.", "The check was not run."], "type": "string"}, "stateMetadata": {"$ref": "GoogleChecksReportV1alphaCheckStateMetadata", "description": "Additional information about the check state in relation to past reports."}, "type": {"description": "The type of check that was run. A type will only appear once in a report's list of checks.", "enum": ["CHECK_TYPE_UNSPECIFIED", "STORE_LISTING_PRIVACY_POLICY_LINK_PRESENT", "PRIVACY_POLICY_UPDATE_DATE_RECENT", "PRIVACY_POLICY_GDPR_GENERAL_RULES", "PRIVACY_POLICY_CCPA_GENERAL_RULES", "PRIVACY_POLICY_COLLECTION_CATEGORIES_DATA_NOTICE", "PRIVACY_POLICY_PROCESSING_PURPOSE_DATA_NOTICE", "PRIVACY_POLICY_SHARING_CATEGORIES_DATA_NOTICE", "PRIVACY_POLICY_DATA_RETENTION_NOTICE", "PRIVACY_POLICY_CONTACT_DETAILS_NOTICE", "PRIVACY_POLICY_CHILDREN_GENERAL_RULES", "PRIVACY_POLICY_DATA_TYPE_PHONE_NUMBER", "PRIVACY_POLICY_DATA_TYPE_USER_ACCOUNT_INFO", "PRIVACY_POLICY_DATA_TYPE_PRECISE_LOCATION", "PRIVACY_POLICY_DATA_TYPE_DEVICE_ID", "PRIVACY_POLICY_DATA_TYPE_APPS_ON_DEVICE", "PRIVACY_POLICY_DATA_TYPE_CONTACTS", "PRIVACY_POLICY_DATA_TYPE_TEXT_MESSAGES", "PRIVACY_POLICY_DATA_TYPE_PII", "PRIVACY_POLICY_DATA_TYPE_PII_CATEGORIES", "PRIVACY_POLICY_DATA_TYPE_HEALTH_AND_BIOMETRIC", "PRIVACY_POLICY_BRAZIL_LGPD_GENERAL_RULES", "PRIVACY_POLICY_VIRGINIA_VCDPA_GENERAL_RULES", "PRIVACY_POLICY_AFFILIATION_MENTION", "PRIVACY_POLICY_RIGHT_TO_DELETE_NOTICE", "PRIVACY_POLICY_RIGHT_TO_ACCESS_NOTICE", "PRIVACY_POLICY_RIGHT_TO_RECTIFICATION_NOTICE", "PRIVACY_POLICY_RIGHT_TO_KNOW_ABOUT_SELLING_NOTICE", "PRIVACY_POLICY_RIGHT_TO_KNOW_ABOUT_SHARING_NOTICE", "PRIVACY_POLICY_RIGHT_TO_OPT_OUT_FROM_SELLING_NOTICE", "PRIVACY_POLICY_METHOD_TO_OPT_OUT_FROM_SELLING_OR_SHARING_NOTICE", "PRIVACY_POLICY_DATA_CONTROLLER_IDENTITY", "PRIVACY_POLICY_DPO_CONTACT_DETAILS", "PRIVACY_POLICY_RIGHT_TO_LODGE_A_COMPLAINT", "PRIVACY_POLICY_LEGAL_BASIS", "PRIVACY_POLICY_CHILDREN_INFO_COLLECTION", "PRIVACY_POLICY_CHILDREN_INFO_USAGE_PURPOSES", "PRIVACY_POLICY_CHILDREN_INFO_DISCLOSURE_PRACTICES", "PRIVACY_POLICY_CHILDREN_INFO_PUBLICITY", "PRIVACY_POLICY_PARENTS_METHOD_OF_INFO_DELETION", "PRIVACY_POLICY_PARENTS_METHOD_TO_INFO_REVIEW", "PRIVACY_POLICY_PARENTS_METHOD_TO_STOP_FURTHER_INFO_COLLECTION_USE", "PRIVACY_POLICY_PARENTS_RIGHT_TO_INFO_DELETION", "PRIVACY_POLICY_PARENTS_RIGHT_TO_INFO_REVIEW", "PRIVACY_POLICY_PARENTS_RIGHT_TO_STOP_FURTHER_INFO_COLLECTION_USE", "PRIVACY_POLICY_PSL_APPROXIMATE_LOCATION", "PRIVACY_POLICY_PSL_PRECISE_LOCATION", "PRIVACY_POLICY_PSL_NAME", "PRIVACY_POLICY_PSL_EMAIL_ADDRESS", "PRIVACY_POLICY_PSL_USER_IDENTIFIERS", "PRIVACY_POLICY_PSL_ADDRESS", "PRIVACY_POLICY_PSL_PHONE_NUMBER", "PRIVACY_POLICY_PSL_RACE_AND_ETHNICITY", "PRIVACY_POLICY_PSL_CREDIT_SCORE", "PRIVACY_POLICY_PSL_PURCHASE_HISTORY", "PRIVACY_POLICY_PSL_HEALTH_INFO", "PRIVACY_POLICY_PSL_FITNESS_INFO", "PRIVACY_POLICY_PSL_EMAIL_MESSAGES", "PRIVACY_POLICY_PSL_TEXT_MESSAGES", "PRIVACY_POLICY_PSL_PHOTOS", "PRIVACY_POLICY_PSL_VIDEOS", "PRIVACY_POLICY_PSL_MUSIC_FILES", "PRIVACY_POLICY_PSL_VOICE_OR_SOUND_RECORDINGS", "PRIVACY_POLICY_PSL_FILES_AND_DOCS", "PRIVACY_POLICY_PSL_CALENDAR_EVENTS", "PRIVACY_POLICY_PSL_CONTACTS", "PRIVACY_POLICY_PSL_APP_INTERACTIONS", "PRIVACY_POLICY_PSL_IN_APP_SEARCH_HISTORY", "PRIVACY_POLICY_PSL_WEB_BROWSING_HISTORY", "PRIVACY_POLICY_PSL_INSTALLED_APPS", "PRIVACY_POLICY_PSL_CRASH_LOGS", "PRIVACY_POLICY_PSL_DIAGNOSTICS", "PRIVACY_POLICY_PSL_DEVICE_OR_OTHER_IDS", "DATA_MONITORING_NEW_ENDPOINT", "DATA_MONITORING_NEW_PERMISSION", "DATA_MONITORING_NEW_DATA_TYPE", "DATA_MONITORING_NEW_SDK", "DATA_MONITORING_ENCRYPTION", "DATA_MONITORING_NEW_DATA_TYPE_VERSION_DIFF", "DATA_MONITORING_NEW_ENDPOINT_VERSION_DIFF", "DATA_MONITORING_NEW_PERMISSION_VERSION_DIFF", "DATA_MONITORING_NEW_SDK_VERSION_DIFF", "DATA_MONITORING_SDKS_DENYLIST_VIOLATION", "DATA_MONITORING_PERMISSIONS_DENYLIST_VIOLATION", "DATA_MONITORING_ENDPOINTS_DENYLIST_VIOLATION", "DATA_MONITORING_OUTDATED_SDK_VERSION", "DATA_MONITORING_CRITICAL_SDK_ISSUE"], "enumDescriptions": ["Not specified.", "Checks that your store listing includes a working link to your privacy policy.", "Checks that your privacy policy has been updated recently.", "Checks if your privacy policy references rights under GDPR for users in the EU.", "Checks if your privacy policy references rights under the CCPA.", "Checks if your privacy policy mentions the categories of personal data that are collected.", "Checks if your privacy policy explains why personal data is processed.", "Checks if your privacy policy includes information about third-party sharing of personal data.", "Checks if your privacy policy describes your data retention practices.", "Checks if contact information is included in your privacy policy.", "Checks if information about requirements related to children is included in your privacy policy.", "Checks if the Phone Number data type declaration in your privacy policy matches usage.", "Checks if the User Account Info data type declaration in your privacy policy matches usage.", "Checks if the Precise Location data type declaration in your privacy policy matches usage.", "Checks if the Device ID data type declaration in your privacy policy matches usage.", "Checks if the Apps on Device data type declaration in your privacy policy matches usage.", "Checks if the Contacts data type declaration in your privacy policy matches usage.", "Checks if the Text Messages data type declaration in your privacy policy matches usage.", "Checks if the PII data type declaration in your privacy policy matches usage.", "Checks if the PII Categories data type declaration in your privacy policy matches usage.", "Checks if the Health and Biometric data type declaration in your privacy policy matches usage.", "Checks if your privacy policy references rights under LGPD for users in Brazil.", "Checks if your privacy policy references rights under VCDPA for users in Virginia.", "Checks if your privacy policy identifies your company or app name(s).", "Checks if your privacy policy mentions your users' right to delete their data.", "Checks if your privacy policy mentions your users' right to access the data held about them.", "Checks if your privacy policy mentions your users' right to correct inaccuracies within their data.", "Checks if your privacy policy mentions your users' right to know about information selling.", "Checks if your privacy policy mentions your users' right to know about information sharing.", "Checks if your privacy policy mentions your users' right to opt out from information selling.", "Checks if your privacy policy explains how your users opt out from the selling or sharing of their data.", "Checks if your privacy policy provides the name and contact information for your data controller.", "Checks if your privacy policy provides the name and contact information for your Data Protection Officer.", "Checks if your privacy policy mentions your users' right to lodge a complaint with a supervisory authority.", "Checks if your privacy policy mentions the legal basis you rely on for processing your users' data.", "Checks if your privacy policy mentions what personal information is collected from children.", "Checks if your privacy policy mentions why you collect personal information from children.", "Checks if your privacy policy mentions what personal information from children is shared with third parties.", "Checks if your privacy policy mentions whether your app allows children to make their personal information publicly available.", "Checks if your privacy policy mentions how parents/caregivers/guardians can request the deletion of their child's personal information.", "Checks if your privacy policy mentions how parents/caregivers/guardians can review their child's personal information.", "Checks if your privacy policy explains how a parent/caregiver/guardian can stop the collection/use from their child's personal information.", "Checks if your privacy policy mentions the right of a parent/caregiver/guardian to request the deletion of their child's personal information.", "Checks if your privacy policy mentions the right of a parent/caregiver/guardian to review their child's personal information.", "Checks if your privacy policy mentions the right of a parent/caregiver/guardian to stop collection/use from their child's personal information.", "Checks if your privacy policy mentions collection of your users' approximate location if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' precise location if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' personal names if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' email addresses if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' user IDs if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' physical addresses if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' phone numbers if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' race or ethnicity if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' credit score if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' purchase history if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' health info if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' fitness info if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' emails if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' text messages if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' photos if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' videos if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' music files if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' voice or sound recordings if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' files or documents if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' calendar events if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' contacts if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' app interactions if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' in-app search history if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' web browsing history if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' installed apps if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' crash logs if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' performance diagnostics if this data type is declared in your Play Data Safety Section.", "Checks if your privacy policy mentions collection of your users' device or other IDs if this data type is declared in your Play Data Safety Section.", "Checks if there is a new endpoint we've recently detected. Because this check accounts for flakiness, it may fail for several weeks even if the endpoint is not detected in the current report.", "Checks if there is a new permission we've recently detected. Because this check accounts for flakiness, it may fail for several weeks even if the permission is not detected in the current report.", "Checks if there is a new data type we've recently detected. Because this check accounts for flakiness, it may fail for several weeks even if the data type is not detected in the current report.", "Checks if there is a new SDK we've recently detected. Because this check accounts for flakiness, it may fail for several weeks even if the SDK is not detected in the current report.", "Checks if there is any endpoint contacted using HTTP protocol instead of HTTPS. If no protocol is found in the URL, the endpoint is not considered for analysis.", "Checks if new data types have been detected since a specific app version.", "Checks if new endpoints have been detected since a specific app version.", "Checks if new permissions have been detected since a specific app version.", "Checks if new SDKs have been detected since a specific app version.", "Checks if any SDKs were detected that are specified in the denylist.", "Checks if any permissions were detected that are specified in the denylist.", "Checks if any endpoints were detected that are specified in the denylist.", "Checks if there are any outdated SDKs.", "Checks if there are any SDKs with critical issues."], "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckCitation": {"description": "Regulation or policy that serves as the legal basis for the check.", "id": "GoogleChecksReportV1alphaCheckCitation", "properties": {"type": {"description": "Citation type.", "enum": ["CITATION_TYPE_UNSPECIFIED", "COPPA", "GDPR", "FERPA", "CAL_OPPA", "CCPA", "SOPIPA", "LGPD", "CPRA", "VCDPA", "GOOGLE_PLAY_POLICY", "APP_STORE_POLICY", "CPA", "CTDPA", "UCPA", "PIPEDA", "ALBERTA_PIPA", "QUEBEC_ACT", "QUEBEC_BILL_64", "CHINA_PIPL"], "enumDescriptions": ["Not specified.", "Children's Online Privacy Protection Act.", "General Data Protection Regulation.", "Family Educational Rights and Privacy Act.", "The California Online Privacy Protection Act.", "California Consumer Privacy Act.", "Student Online Personal Information Protection Act.", "Lei Geral de Proteção de Dados.", "California Consumer Privacy Act.", "Virginia Consumer Data Protection Act.", "Google Play Policy.", "App Store Policy.", "Colorado Privacy Act.", "Connecticut Data Privacy Act.", "Utah Consumer Privacy Act.", "Personal Information Protection and Electronic Documents Act.", "Alberta (Canada) Personal Information Protection Act.", "Quebec: Act Respecting the Protection of Personal Information in the Private Sector.", "Quebec Bill 64: An Act to Modernize Legislative Provisions as Regards the Protection of Personal Information.", "China Personal Information Protection Law."], "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckDataSecurityEvidence": {"description": "Evidence concerning data security.", "id": "GoogleChecksReportV1alphaCheckDataSecurityEvidence", "properties": {"dataInTransitInfo": {"description": "Evidence related to data in transit.", "items": {"$ref": "GoogleChecksReportV1alphaCheckDataSecurityEvidenceDataInTransitInfo"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckDataSecurityEvidenceDataInTransitInfo": {"description": "Evidence related to data in transit detected in your app.", "id": "GoogleChecksReportV1alphaCheckDataSecurityEvidenceDataInTransitInfo", "properties": {"uri": {"description": "The URL contacted by your app. This includes the protocol, domain, and URL parameters.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckDataTypeEvidence": {"description": "Evidence concerning a data type that was found in your app.", "id": "GoogleChecksReportV1alphaCheckDataTypeEvidence", "properties": {"dataType": {"description": "The data type that was found in your app.", "enum": ["DATA_TYPE_UNSPECIFIED", "DATA_TYPE_APPROXIMATE_LOCATION", "DATA_TYPE_PRECISE_LOCATION", "DATA_TYPE_PERSONAL_NAME", "DATA_TYPE_EMAIL_ADDRESS", "DATA_TYPE_USER_IDS", "DATA_TYPE_PHYSICAL_ADDRESS", "DATA_TYPE_PHONE_NUMBER", "DATA_TYPE_RACE_AND_ETHNICITY", "DATA_TYPE_POLITICAL_OR_RELIGIOUS_BELIEFS", "DATA_TYPE_SEXUAL_ORIENTATION", "DATA_TYPE_OTHER_PERSONAL_INFO", "DATA_TYPE_PAYMENT_INFO", "DATA_TYPE_PURCHASE_HISTORY", "DATA_TYPE_CREDIT_SCORE", "DATA_TYPE_OTHER_FINANCIAL_INFO", "DATA_TYPE_HEALTH_INFO", "DATA_TYPE_FITNESS_INFO", "DATA_TYPE_EMAILS", "DATA_TYPE_TEXT_MESSAGES", "DATA_TYPE_PHOTOS", "DATA_TYPE_VIDEOS", "DATA_TYPE_VOICE_OR_SOUND_RECORDINGS", "DATA_TYPE_MUSIC_FILES", "DATA_TYPE_OTHER_AUDIO_FILES", "DATA_TYPE_FILES_AND_DOCS", "DATA_TYPE_CALENDAR_EVENTS", "DATA_TYPE_CONTACTS", "DATA_TYPE_APP_INTERACTIONS", "DATA_TYPE_IN_APP_SEARCH_HISTORY", "DATA_TYPE_INSTALLED_APPS", "DATA_TYPE_OTHER_USER_GENERATED_CONTENT", "DATA_TYPE_OTHER_ACTIONS", "DATA_TYPE_WEB_BROWSING_HISTORY", "DATA_TYPE_CRASH_LOGS", "DATA_TYPE_PERFORMANCE_DIAGNOSTICS", "DATA_TYPE_OTHER_APP_PERFORMANCE_DATA", "DATA_TYPE_DEVICE_OR_OTHER_IDS"], "enumDescriptions": ["Not specified.", "User or device physical location to an area greater than or equal to 3 square kilometers, such as the city a user is in, or location provided by Android's ACCESS_COARSE_LOCATION permission.", "User or device physical location within an area less than 3 square kilometers, such as location provided by Android's ACCESS_FINE_LOCATION permission.", "How a user refers to themselves, such as their first or last name, or nickname.", "A user's email address.", "Identifiers that relate to an identifiable person. For example, an account ID, account number, or account name.", "A user's address, such as a mailing or home address.", "A user's phone number.", "Information about a user's race or ethnicity.", "Information about a user's political or religious beliefs.", "Information about a user's sexual orientation.", "Any other personal information such as date of birth, gender identity, veteran status, etc.", "Information about a user's financial accounts such as credit card number.", "Information about purchases or transactions a user has made.", "Information about a user's credit score.", "Any other financial information such as user salary or debts.", "Information about a user's health, such as medical records or symptoms.", "Information about a user's fitness, such as exercise or other physical activity.", "A user's emails including the email subject line, sender, recipients, and the content of the email.", "A user's text messages including the sender, recipients, and the content of the message.", "A user's photos.", "A user's videos.", "A user's voice such as a voicemail or a sound recording.", "A user's music files.", "Any other user-created or user-provided audio files.", "A user's files or documents, or information about their files or documents such as file names.", "Information from a user's calendar such as events, event notes, and attendees.", "Information about the user’s contacts such as contact names, message history, and social graph information like usernames, contact recency, contact frequency, interaction duration and call history.", "Information about how a user interacts with your app, such as the number of page views or taps.", "Information about what a user has searched for in your app.", "Inventory of apps or packages installed on the user’s device.", "Any other user-generated content not listed here, or in any other section. For example, user bios, notes, or open-ended responses.", "Any other user activity or actions in-app not listed here such as gameplay, likes, and dialog options.", "Information about the websites a user has visited.", "Crash log data from your app. For example, the number of times your app has crashed, stack traces, or other information directly related to a crash.", "Information about the performance of your app. For example battery life, loading time, latency, framerate, or any technical diagnostics.", "Any other app performance data not listed here.", "Identifiers that relate to an individual device, browser or app. For example, an IMEI number, MAC address, Widevine Device ID, Firebase installation ID, or advertising identifier."], "type": "string"}, "dataTypeEvidence": {"$ref": "GoogleChecksReportV1alphaDataTypeEvidence", "description": "Evidence collected about the data type."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckEndpointEvidence": {"description": "Evidence concerning an endpoint that was contacted by your app.", "id": "GoogleChecksReportV1alphaCheckEndpointEvidence", "properties": {"endpoint": {"$ref": "GoogleChecksReportV1alphaEndpoint", "description": "The endpoint that was contacted by your app."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidence": {"description": "Evidence collected from endpoint restriction violation analysis.", "id": "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidence", "properties": {"endpointDetails": {"description": "Endpoints in violation.", "items": {"$ref": "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidenceEndpointDetails"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidenceEndpointDetails": {"description": "Details of the endpoint in violation.", "id": "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidenceEndpointDetails", "properties": {"endpoint": {"$ref": "GoogleChecksReportV1alphaEndpoint", "description": "The endpoint in violation."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckEvidence": {"description": "Evidence for a check.", "id": "GoogleChecksReportV1alphaCheckEvidence", "properties": {"dataSecurity": {"$ref": "GoogleChecksReportV1alphaCheckDataSecurityEvidence", "description": "Evidence concerning data security."}, "dataTypes": {"description": "Evidence concerning data types found in your app.", "items": {"$ref": "GoogleChecksReportV1alphaCheckDataTypeEvidence"}, "type": "array"}, "endpointRestrictionViolations": {"description": "Evidence collected from endpoint restriction violation analysis.", "items": {"$ref": "GoogleChecksReportV1alphaCheckEndpointRestrictionViolationEvidence"}, "type": "array"}, "endpoints": {"description": "Evidence concerning endpoints that were contacted by your app.", "items": {"$ref": "GoogleChecksReportV1alphaCheckEndpointEvidence"}, "type": "array"}, "permissionRestrictionViolations": {"description": "Evidence collected from permission restriction violation analysis.", "items": {"$ref": "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidence"}, "type": "array"}, "permissions": {"description": "Evidence concerning permissions that were found in your app.", "items": {"$ref": "GoogleChecksReportV1alphaCheckPermissionEvidence"}, "type": "array"}, "privacyPolicyTexts": {"description": "Evidence collected from your privacy policy(s).", "items": {"$ref": "GoogleChecksReportV1alphaCheckPrivacyPolicyTextEvidence"}, "type": "array"}, "sdkIssues": {"description": "Evidence concerning SDK issues.", "items": {"$ref": "GoogleChecksReportV1alphaCheckSdkIssueEvidence"}, "type": "array"}, "sdkRestrictionViolations": {"description": "Evidence collected from SDK restriction violation analysis.", "items": {"$ref": "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidence"}, "type": "array"}, "sdks": {"description": "Evidence concerning SDKs that were found in your app.", "items": {"$ref": "GoogleChecksReportV1alphaCheckSdkEvidence"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckPermissionEvidence": {"description": "Evidence concerning a permission that was found in your app.", "id": "GoogleChecksReportV1alphaCheckPermissionEvidence", "properties": {"permission": {"$ref": "GoogleChecksReportV1alphaPermission", "description": "The permission that was found in your app."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidence": {"description": "Evidence collected from permission restriction violation analysis.", "id": "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidence", "properties": {"permissionDetails": {"description": "Permissions in violation.", "items": {"$ref": "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidencePermissionDetails"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidencePermissionDetails": {"description": "Details of the permission in violation.", "id": "GoogleChecksReportV1alphaCheckPermissionRestrictionViolationEvidencePermissionDetails", "properties": {"permission": {"$ref": "GoogleChecksReportV1alphaPermission", "description": "The permission in violation."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckPrivacyPolicyTextEvidence": {"description": "Evidence collected from your privacy policy(s).", "id": "GoogleChecksReportV1alphaCheckPrivacyPolicyTextEvidence", "properties": {"policyFragment": {"$ref": "GoogleChecksReportV1alphaPolicyFragment", "description": "The privacy policy fragment that was used during the check."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckSdkEvidence": {"description": "Evidence conerning an SDK that was found in your app.", "id": "GoogleChecksReportV1alphaCheckSdkEvidence", "properties": {"sdk": {"$ref": "GoogleChecksReportV1alphaSdk", "description": "The SDK that was found in your app."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckSdkIssueEvidence": {"description": "Evidence concerning an SDK issue.", "id": "GoogleChecksReportV1alphaCheckSdkIssueEvidence", "properties": {"sdk": {"$ref": "GoogleChecksReportV1alphaSdk", "description": "The SDK with an issue."}, "sdkVersion": {"description": "The SDK version.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidence": {"description": "Evidence collected from SDK restriction violation analysis.", "id": "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidence", "properties": {"sdkDetails": {"description": "SDKs in violation.", "items": {"$ref": "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidenceSdkDetails"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidenceSdkDetails": {"description": "Details of the SDK in violation.", "id": "GoogleChecksReportV1alphaCheckSdkRestrictionViolationEvidenceSdkDetails", "properties": {"sdk": {"$ref": "GoogleChecksReportV1alphaSdk", "description": "The SDK in violation."}}, "type": "object"}, "GoogleChecksReportV1alphaCheckStateMetadata": {"description": "Additional information about the check state in relation to past reports.", "id": "GoogleChecksReportV1alphaCheckStateMetadata", "properties": {"badges": {"description": "Indicators related to the check state.", "items": {"enum": ["CHECK_STATE_BADGE_UNSPECIFIED", "NEWLY_FAILING", "RECENTLY_FAILING", "RESOLVED"], "enumDescriptions": ["Not specified.", "The check is newly failing, i.e. now failing but previously passing.", "The check is currently failing and first started failing continuously within the last 28 days.", "The check is newly passing, i.e. now passing but previously failing."], "type": "string"}, "type": "array"}, "firstFailingTime": {"description": "The time when the check first started failing.", "format": "google-datetime", "type": "string"}, "lastFailingTime": {"description": "The last time the check failed.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoring": {"description": "Represents the data monitoring section of the report.", "id": "GoogleChecksReportV1alphaDataMonitoring", "properties": {"dataTypes": {"description": "Data types that your app shares or collects.", "items": {"$ref": "GoogleChecksReportV1alphaDataMonitoringDataTypeResult"}, "type": "array"}, "endpoints": {"description": "Endpoints that were found by dynamic analysis of your app.", "items": {"$ref": "GoogleChecksReportV1alphaDataMonitoringEndpointResult"}, "type": "array"}, "permissions": {"description": "Permissions that your app uses.", "items": {"$ref": "GoogleChecksReportV1alphaDataMonitoringPermissionResult"}, "type": "array"}, "sdks": {"description": "SDKs that your app uses.", "items": {"$ref": "GoogleChecksReportV1alphaDataMonitoringSdkResult"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoringDataTypeResult": {"description": "Information about a data type that was found in your app.", "id": "GoogleChecksReportV1alphaDataMonitoringDataTypeResult", "properties": {"dataType": {"description": "The data type that was shared or collected by your app.", "enum": ["DATA_TYPE_UNSPECIFIED", "DATA_TYPE_APPROXIMATE_LOCATION", "DATA_TYPE_PRECISE_LOCATION", "DATA_TYPE_PERSONAL_NAME", "DATA_TYPE_EMAIL_ADDRESS", "DATA_TYPE_USER_IDS", "DATA_TYPE_PHYSICAL_ADDRESS", "DATA_TYPE_PHONE_NUMBER", "DATA_TYPE_RACE_AND_ETHNICITY", "DATA_TYPE_POLITICAL_OR_RELIGIOUS_BELIEFS", "DATA_TYPE_SEXUAL_ORIENTATION", "DATA_TYPE_OTHER_PERSONAL_INFO", "DATA_TYPE_PAYMENT_INFO", "DATA_TYPE_PURCHASE_HISTORY", "DATA_TYPE_CREDIT_SCORE", "DATA_TYPE_OTHER_FINANCIAL_INFO", "DATA_TYPE_HEALTH_INFO", "DATA_TYPE_FITNESS_INFO", "DATA_TYPE_EMAILS", "DATA_TYPE_TEXT_MESSAGES", "DATA_TYPE_PHOTOS", "DATA_TYPE_VIDEOS", "DATA_TYPE_VOICE_OR_SOUND_RECORDINGS", "DATA_TYPE_MUSIC_FILES", "DATA_TYPE_OTHER_AUDIO_FILES", "DATA_TYPE_FILES_AND_DOCS", "DATA_TYPE_CALENDAR_EVENTS", "DATA_TYPE_CONTACTS", "DATA_TYPE_APP_INTERACTIONS", "DATA_TYPE_IN_APP_SEARCH_HISTORY", "DATA_TYPE_INSTALLED_APPS", "DATA_TYPE_OTHER_USER_GENERATED_CONTENT", "DATA_TYPE_OTHER_ACTIONS", "DATA_TYPE_WEB_BROWSING_HISTORY", "DATA_TYPE_CRASH_LOGS", "DATA_TYPE_PERFORMANCE_DIAGNOSTICS", "DATA_TYPE_OTHER_APP_PERFORMANCE_DATA", "DATA_TYPE_DEVICE_OR_OTHER_IDS"], "enumDescriptions": ["Not specified.", "User or device physical location to an area greater than or equal to 3 square kilometers, such as the city a user is in, or location provided by Android's ACCESS_COARSE_LOCATION permission.", "User or device physical location within an area less than 3 square kilometers, such as location provided by Android's ACCESS_FINE_LOCATION permission.", "How a user refers to themselves, such as their first or last name, or nickname.", "A user's email address.", "Identifiers that relate to an identifiable person. For example, an account ID, account number, or account name.", "A user's address, such as a mailing or home address.", "A user's phone number.", "Information about a user's race or ethnicity.", "Information about a user's political or religious beliefs.", "Information about a user's sexual orientation.", "Any other personal information such as date of birth, gender identity, veteran status, etc.", "Information about a user's financial accounts such as credit card number.", "Information about purchases or transactions a user has made.", "Information about a user's credit score.", "Any other financial information such as user salary or debts.", "Information about a user's health, such as medical records or symptoms.", "Information about a user's fitness, such as exercise or other physical activity.", "A user's emails including the email subject line, sender, recipients, and the content of the email.", "A user's text messages including the sender, recipients, and the content of the message.", "A user's photos.", "A user's videos.", "A user's voice such as a voicemail or a sound recording.", "A user's music files.", "Any other user-created or user-provided audio files.", "A user's files or documents, or information about their files or documents such as file names.", "Information from a user's calendar such as events, event notes, and attendees.", "Information about the user’s contacts such as contact names, message history, and social graph information like usernames, contact recency, contact frequency, interaction duration and call history.", "Information about how a user interacts with your app, such as the number of page views or taps.", "Information about what a user has searched for in your app.", "Inventory of apps or packages installed on the user’s device.", "Any other user-generated content not listed here, or in any other section. For example, user bios, notes, or open-ended responses.", "Any other user activity or actions in-app not listed here such as gameplay, likes, and dialog options.", "Information about the websites a user has visited.", "Crash log data from your app. For example, the number of times your app has crashed, stack traces, or other information directly related to a crash.", "Information about the performance of your app. For example battery life, loading time, latency, framerate, or any technical diagnostics.", "Any other app performance data not listed here.", "Identifiers that relate to an individual device, browser or app. For example, an IMEI number, MAC address, Widevine Device ID, Firebase installation ID, or advertising identifier."], "type": "string"}, "dataTypeEvidence": {"$ref": "GoogleChecksReportV1alphaDataTypeEvidence", "description": "Evidence collected about the data type."}, "metadata": {"$ref": "GoogleChecksReportV1alphaDataMonitoringResultMetadata", "description": "<PERSON><PERSON><PERSON> about the result."}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoringEndpointResult": {"description": "Information about an endpoint that was contacted by your app.", "id": "GoogleChecksReportV1alphaDataMonitoringEndpointResult", "properties": {"endpoint": {"$ref": "GoogleChecksReportV1alphaEndpoint", "description": "The endpoint that was contacted by your app."}, "hitCount": {"description": "The number of times this endpoint was contacted by your app.", "format": "int32", "type": "integer"}, "metadata": {"$ref": "GoogleChecksReportV1alphaDataMonitoringResultMetadata", "description": "<PERSON><PERSON><PERSON> about the result."}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoringPermissionResult": {"description": "Information about a permission that was found in your app.", "id": "GoogleChecksReportV1alphaDataMonitoringPermissionResult", "properties": {"metadata": {"$ref": "GoogleChecksReportV1alphaDataMonitoringResultMetadata", "description": "<PERSON><PERSON><PERSON> about the result."}, "permission": {"$ref": "GoogleChecksReportV1alphaPermission", "description": "The permission that was found in your app."}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoringResultMetadata": {"description": "Information about a data monitoring result.", "id": "GoogleChecksReportV1alphaDataMonitoringResultMetadata", "properties": {"badges": {"description": "Badges that apply to this result.", "items": {"enum": ["DATA_MONITORING_RESULT_BADGE_UNSPECIFIED", "NEW"], "enumDescriptions": ["Not specified.", "Indicates a newly detected result in the data monitoring report."], "type": "string"}, "type": "array"}, "firstDetectedTime": {"description": "The timestamp when this result was first detected within the last 8 weeks. If not set, it wasn't detected within the last 8 weeks.", "format": "google-datetime", "type": "string"}, "lastDetectedAppVersion": {"description": "Your app's version name when this result was last detected within the last 8 weeks. If not set, it wasn't detected within the last 8 weeks.", "type": "string"}, "lastDetectedTime": {"description": "The timestamp when this result was last detected within the last 8 weeks. If not set, it wasn't detected within the last 8 weeks.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaDataMonitoringSdkResult": {"description": "Information about an SDK that was found in your app.", "id": "GoogleChecksReportV1alphaDataMonitoringSdkResult", "properties": {"metadata": {"$ref": "GoogleChecksReportV1alphaDataMonitoringResultMetadata", "description": "<PERSON><PERSON><PERSON> about the result."}, "sdk": {"$ref": "GoogleChecksReportV1alphaSdk", "description": "The SDK that was found in your app."}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypeEndpointEvidence": {"description": "Evidence based on an endpoint that data was sent to.", "id": "GoogleChecksReportV1alphaDataTypeEndpointEvidence", "properties": {"attributedSdks": {"description": "Set of SDKs that are attributed to the exfiltration.", "items": {"$ref": "GoogleChecksReportV1alphaDataTypeEndpointEvidenceAttributedSdk"}, "type": "array"}, "endpointDetails": {"description": "Endpoints the data type was sent to.", "items": {"$ref": "GoogleChecksReportV1alphaDataTypeEndpointEvidenceEndpointDetails"}, "type": "array"}, "exfiltratedDataType": {"description": "Type of data that was exfiltrated.", "enum": ["EXFILTRATED_DATA_TYPE_UNSPECIFIED", "EXFILTRATED_DATA_TYPE_PHONE_NUMBER", "EXFILTRATED_DATA_TYPE_PRECISE_LOCATION", "EXFILTRATED_DATA_TYPE_CONTACT_NAME", "EXFILTRATED_DATA_TYPE_CONTACT_EMAIL", "EXFILTRATED_DATA_TYPE_CONTACT_PHONE_NUMBER", "EXFILTRATED_DATA_TYPE_INCOMING_TEXT_NUMBER", "EXFILTRATED_DATA_TYPE_INCOMING_TEXT_MESSAGE", "EXFILTRATED_DATA_TYPE_OUTGOING_TEXT_NUMBER", "EXFILTRATED_DATA_TYPE_OUTGOING_TEXT_MESSAGE", "EXFILTRATED_DATA_TYPE_ADVERTISING_ID", "EXFILTRATED_DATA_TYPE_ANDROID_ID", "EXFILTRATED_DATA_TYPE_IMEI", "EXFILTRATED_DATA_TYPE_IMSI", "EXFILTRATED_DATA_TYPE_SIM_SERIAL_NUMBER", "EXFILTRATED_DATA_TYPE_SSID", "EXFILTRATED_DATA_TYPE_ACCOUNT", "EXFILTRATED_DATA_TYPE_EXTERNAL_ACCOUNT", "EXFILTRATED_DATA_TYPE_INSTALLED_PACKAGES"], "enumDescriptions": ["Not specified.", "The user's phone number.", "The user's precise location.", "Name of one or more contacts from the user's phone.", "Email of one or more contacts from the user's phone.", "Phone number of one or more contacts from the user's phone.", "Phone number of an incoming text message.", "Content of an incoming text message.", "Phone number of an outgoing text message.", "Content of an outgoing text message.", "Advertising ID.", "Android ID.", "IMEI.", "IMSI.", "Sim serial number.", "SSID: Service Set IDentifier, i.e. the network's name.", "Information about the main account of the device.", "Information about an external account, e.g. Facebook, Twitter.", "One or more of the package names of apps on the device."], "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypeEndpointEvidenceAttributedSdk": {"description": "Details of SDK that is attributed to the exfiltration.", "id": "GoogleChecksReportV1alphaDataTypeEndpointEvidenceAttributedSdk", "properties": {"sdk": {"$ref": "GoogleChecksReportV1alphaSdk", "description": "SDK that is attributed to the exfiltration."}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypeEndpointEvidenceEndpointDetails": {"description": "Details of the endpoint the data type was sent to.", "id": "GoogleChecksReportV1alphaDataTypeEndpointEvidenceEndpointDetails", "properties": {"endpoint": {"$ref": "GoogleChecksReportV1alphaEndpoint", "description": "Endpoint the data type was sent to."}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypeEvidence": {"description": "Evidence collected about a data type.", "id": "GoogleChecksReportV1alphaDataTypeEvidence", "properties": {"endpoints": {"description": "List of endpoints the data type was sent to.", "items": {"$ref": "GoogleChecksReportV1alphaDataTypeEndpointEvidence"}, "type": "array"}, "permissions": {"description": "List of included permissions that imply collection of the data type.", "items": {"$ref": "GoogleChecksReportV1alphaDataTypePermissionEvidence"}, "type": "array"}, "privacyPolicyTexts": {"description": "List of privacy policy texts that imply collection of the data type.", "items": {"$ref": "GoogleChecksReportV1alphaDataTypePrivacyPolicyTextEvidence"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypePermissionEvidence": {"description": "Evidence based on the inclusion of a permission.", "id": "GoogleChecksReportV1alphaDataTypePermissionEvidence", "properties": {"permission": {"$ref": "GoogleChecksReportV1alphaPermission", "description": "Permission declared by your app."}}, "type": "object"}, "GoogleChecksReportV1alphaDataTypePrivacyPolicyTextEvidence": {"description": "Evidence based on information from the privacy policy.", "id": "GoogleChecksReportV1alphaDataTypePrivacyPolicyTextEvidence", "properties": {"policyFragment": {"$ref": "GoogleChecksReportV1alphaPolicyFragment", "description": "The privacy policy fragment that implies collection of the data type."}}, "type": "object"}, "GoogleChecksReportV1alphaEndpoint": {"description": "Information about an endpoint.", "id": "GoogleChecksReportV1alphaEndpoint", "properties": {"domain": {"description": "Domain name (e.g. ads.google.com).", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaListReportsResponse": {"description": "The response message for ReportService.ListReports.", "id": "GoogleChecksReportV1alphaListReportsResponse", "properties": {"nextPageToken": {"description": "A token which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "reports": {"description": "The reports for the specified app.", "items": {"$ref": "GoogleChecksReportV1alphaReport"}, "type": "array"}}, "type": "object"}, "GoogleChecksReportV1alphaPermission": {"description": "Information about a permission.", "id": "GoogleChecksReportV1alphaPermission", "properties": {"id": {"description": "Permission identifier.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaPolicyFragment": {"description": "Information about a policy fragment.", "id": "GoogleChecksReportV1alphaPolicyFragment", "properties": {"htmlContent": {"description": "HTML content.", "type": "string"}, "sourceUri": {"description": "Policy URL.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaReport": {"description": "Privacy report.", "id": "GoogleChecksReportV1alphaReport", "properties": {"appBundle": {"$ref": "GoogleChecksReportV1alphaAppBundle", "description": "Information about the analyzed app bundle."}, "checks": {"description": "List of checks that were run on the app bundle.", "items": {"$ref": "GoogleChecksReportV1alphaCheck"}, "type": "array"}, "dataMonitoring": {"$ref": "GoogleChecksReportV1alphaDataMonitoring", "description": "Information related to data monitoring."}, "name": {"description": "Resource name of the report.", "type": "string"}, "resultsUri": {"description": "A URL to view results.", "type": "string"}}, "type": "object"}, "GoogleChecksReportV1alphaSdk": {"description": "Information about an SDK.", "id": "GoogleChecksReportV1alphaSdk", "properties": {"id": {"description": "SDK identifier.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "WaitOperationRequest": {"description": "The request message for Operations.WaitOperation.", "id": "WaitOperationRequest", "properties": {"timeout": {"description": "The maximum duration to wait before timing out. If left blank, the wait will be at most the time permitted by the underlying HTTP/RPC protocol. If RPC context deadline is also specified, the shorter one will be used.", "format": "google-duration", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Checks API", "version": "v1alpha", "version_module": true}