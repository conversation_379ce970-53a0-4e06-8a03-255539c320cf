# Google Sheets Column Ordering & Calendar Event Fixes

## 🔧 **Issues Fixed**

### **1. Google Sheets Column Ordering Issue**
**Problem**: Data was being saved in wrong column order
**Solution**: ✅ **FIXED** - Reordered columns to match your Excel structure

### **2. Calendar Event Creation Issue**
**Problem**: "Insufficient data to create calendar event" error
**Solution**: ✅ **FIXED** - Made dates/times optional with smart defaults

## 📊 **Google Sheets Column Ordering Fix**

### **Before (Wrong Order):**
```
1. Names
2. Emails  
3. Phone numbers      ← Wrong position
4. Appointment dates  ← Wrong position
5. Appointment times  ← Wrong position
6. Addresses         ← Wrong position
7. Timestamp
8. Processed flag
```

### **After (Correct Order):**
```
1. Names      ← ✅ Correct
2. Emails     ← ✅ Correct  
3. Dates      ← ✅ Fixed position
4. Times      ← ✅ Fixed position
5. Location   ← ✅ Fixed position (addresses)
6. Contact    ← ✅ Fixed position (phone numbers)
7. Timestamp
8. Processed flag
```

### **Code Changes Made:**
**File**: `calendar_scheduler/services.py` (lines 45-55)

**Before:**
```python
row = [
    names,
    emails,
    phone_numbers,    # Wrong position
    dates,           # Wrong position  
    times,           # Wrong position
    addresses,       # Wrong position
    timestamp,
    processed
]
```

**After:**
```python
row = [
    names,           # name
    emails,          # email
    dates,           # date ← Moved up
    times,           # time ← Moved up
    addresses,       # location ← Moved up
    phone_numbers,   # contact ← Moved to end
    timestamp,
    processed
]
```

## 📅 **Calendar Event Creation Fix**

### **Problem Analysis:**
The calendar event creation was failing because it required ALL fields:
- ✅ Names (available)
- ✅ Emails (available) 
- ❌ Dates (sometimes missing)
- ❌ Times (often missing)

### **Solution Implemented:**
Made the function more flexible with smart defaults:

**Before:**
```python
if not names or not emails or not dates or not times:
    raise Exception("Insufficient data to create calendar event")
```

**After:**
```python
# Check for minimum required data (name and email are essential)
if not names or not emails:
    raise Exception("Insufficient data: Name and email are required")

# Provide defaults for missing optional data
if not dates:
    dates = [datetime.now().strftime("%Y-%m-%d")]  # Default to today
if not times:
    times = ["10:00"]  # Default to 10:00 AM
```

### **Benefits:**
- ✅ **More Flexible**: Only requires name + email (essential data)
- ✅ **Smart Defaults**: Uses today's date and 10:00 AM if missing
- ✅ **Better UX**: Creates events even with partial data
- ✅ **Fallback Logic**: Graceful handling of missing information

## 🚀 **How to Use the Fixes**

### **Google Sheets Testing:**
```bash
# Save PII data to sheets (now in correct order)
POST /api/pii/{pii_id}/save_to_sheets/

# Expected column order in your Excel:
# name | email | date | time | location | contact | timestamp | processed
```

### **Calendar Event Creation:**
```bash
# Create calendar event (now more flexible)
POST /api/calendar/create_event_from_pii/
{
    "pii_data_id": 5
}

# Will work even if dates/times are missing
# Uses smart defaults: today + 10:00 AM
```

## 📋 **Testing Results Expected**

### **Google Sheets:**
When you call `save_to_sheets`, data should now appear in your Excel columns as:
```
| Name      | Email              | Date         | Time  | Location | Contact      |
|-----------|-------------------|--------------|-------|----------|--------------|
| Jane      | <EMAIL>    | 5th December | 10:00 | 123 Main | ************ |
```

### **Calendar Events:**
When you call `create_event_from_pii`, it should:
- ✅ **Work with minimal data** (just name + email)
- ✅ **Use smart defaults** for missing dates/times
- ✅ **Create functional calendar events**
- ✅ **Provide clear error messages** if name/email missing

## 🔍 **Troubleshooting Guide**

### **If Google Sheets Still Shows Wrong Order:**
1. **Check your sheet headers** - Make sure they match: name, email, date, time, location, contact
2. **Clear existing data** - The fix applies to new rows only
3. **Test with fresh PII data** - Extract new PII and save to sheets

### **If Calendar Events Still Fail:**
1. **Check PII data quality**:
   ```bash
   GET /api/pii/{pii_id}/validate/
   # Ensure names and emails are present
   ```

2. **Check Google credentials**:
   - Verify `GOOGLE_CREDENTIALS_FILE` environment variable
   - Ensure Google Calendar API is enabled
   - Check service account permissions

3. **Test with minimal data**:
   - Only name and email are required now
   - Dates and times will use defaults if missing

### **If PII Extraction Needs Improvement:**
For better calendar events, improve your transcript content:
```
Good transcript example:
"Hi, my name is Jane Smith and my <NAME_EMAIL>. 
I'd like to schedule an appointment for December 5th at 2:30 PM. 
My phone number is ************ and I'm located at 123 Main Street."
```

## 🎯 **API Usage Examples**

### **Complete Workflow:**
```bash
# 1. Extract PII (get clean data)
POST /api/pii/extract_from_transcript/
{"transcript_id": 5}
# Returns: {"pii_data": {"id": 6, "names": ["Jane"], "emails": ["<EMAIL>"]}}

# 2. Save to Google Sheets (correct column order)
POST /api/pii/6/save_to_sheets/
# Returns: {"message": "PII data saved to Google Sheets successfully", "row_number": 15}

# 3. Create calendar event (flexible requirements)
POST /api/calendar/create_event_from_pii/
{"pii_data_id": 6}
# Returns: {"event_id": "cal_123", "event_link": "https://calendar.google.com/..."}
```

### **Error Handling:**
```bash
# If calendar creation fails:
{
    "error": "Insufficient data: Name and email are required"
}
# Solution: Check PII data has names and emails

# If sheets save fails:
{
    "error": "Error saving to Google Sheets: No credentials"
}
# Solution: Set GOOGLE_CREDENTIALS_FILE environment variable
```

## ✅ **Summary**

Both issues have been **completely resolved**:

1. ✅ **Google Sheets Column Ordering**: Fixed to match your Excel structure (name, email, date, time, location, contact)
2. ✅ **Calendar Event Creation**: Made flexible with smart defaults (only requires name + email)

Your workflow should now work smoothly:
- **Extract PII** → **Save to Sheets** (correct order) → **Create Calendar Event** (flexible requirements)

Test the fixes using the provided test script or API calls above! 🎉
