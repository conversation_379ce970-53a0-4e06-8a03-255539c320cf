#!/usr/bin/env python3
"""
Test script to verify the transcript cleaning fix
"""

import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:8000/api"

def test_transcript_workflow():
    """Test the complete transcript workflow"""
    print("🧪 TESTING TRANSCRIPT WORKFLOW")
    print("=" * 60)
    
    # Test audio file ID (replace with your actual audio file ID)
    audio_id = 2
    
    print(f"Testing with Audio ID: {audio_id}")
    print("-" * 40)
    
    # Step 1: Get current transcripts
    print("📋 Step 1: Getting current transcripts...")
    try:
        response = requests.get(f"{BASE_URL}/audio/{audio_id}/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {data['total_transcripts']} transcripts")
            for transcript in data['transcripts']:
                print(f"   - Transcript ID: {transcript['id']}, Created: {transcript.get('created_at', 'N/A')}")
                if transcript.get('is_most_recent'):
                    print(f"     👆 This is the most recent transcript")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("-" * 40)
    
    # Step 2: Process audio (creates new transcript)
    print("🎵 Step 2: Processing audio (creates new transcript)...")
    try:
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/process/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Audio processed successfully")
            print(f"   New Transcript ID: {data['transcript_id']}")
            new_transcript_id = data['transcript_id']
        else:
            print(f"❌ Error processing audio: {response.status_code}")
            print(response.text)
            return
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return
    
    print("-" * 40)
    
    # Step 3: Get transcripts again to see the new one
    print("📋 Step 3: Getting updated transcripts...")
    try:
        response = requests.get(f"{BASE_URL}/audio/{audio_id}/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Now found {data['total_transcripts']} transcripts")
            for transcript in data['transcripts']:
                print(f"   - Transcript ID: {transcript['id']}, Created: {transcript.get('created_at', 'N/A')}")
                if transcript.get('is_most_recent'):
                    print(f"     👆 This is the most recent transcript")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("-" * 40)
    
    # Step 4: Clean transcript (should clean the most recent one)
    print("🧹 Step 4: Cleaning transcript (should clean most recent)...")
    try:
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/clean_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Transcript cleaned successfully")
            print(f"   Cleaned Transcript ID: {data['transcript_id']}")
            print(f"   Total Transcripts: {data['total_transcripts']}")
            print(f"   Note: {data.get('note', 'N/A')}")
            print(f"   Preview: {data['cleaned_text']}")
        else:
            print(f"❌ Error cleaning transcript: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    print("-" * 40)
    
    # Step 5: Test cleaning a specific transcript
    print("🎯 Step 5: Cleaning a specific transcript...")
    try:
        # Use the new transcript ID we got from step 2
        payload = {"transcript_id": new_transcript_id}
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/clean_transcript/", 
                               json=payload,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Specific transcript cleaned successfully")
            print(f"   Cleaned Transcript ID: {data['transcript_id']}")
            print(f"   Note: {data.get('note', 'N/A')}")
            print(f"   Preview: {data['cleaned_text']}")
        else:
            print(f"❌ Error cleaning specific transcript: {response.status_code}")
            print(response.text)
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_api_endpoints():
    """Test individual API endpoints"""
    print("\n" + "🔗 TESTING API ENDPOINTS")
    print("=" * 60)
    
    # Test endpoints
    endpoints = [
        ("GET", "/audio/", "List audio files"),
        ("GET", "/audio/2/get_transcript/", "Get transcripts for audio 2"),
    ]
    
    for method, endpoint, description in endpoints:
        print(f"Testing: {method} {endpoint} - {description}")
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}")
            else:
                response = requests.post(f"{BASE_URL}{endpoint}")
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ Success")
            else:
                print("   ❌ Error")
                print(f"   Response: {response.text[:100]}...")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
        print("-" * 30)

if __name__ == "__main__":
    print("🚀 TRANSCRIPT CLEANING FIX TEST")
    print("=" * 60)
    print("This script tests the fix for transcript ID confusion")
    print("Make sure the Django server is running on http://127.0.0.1:8000")
    print()
    
    # Test the workflow
    test_transcript_workflow()
    
    # Test individual endpoints
    test_api_endpoints()
    
    print("\n" + "=" * 60)
    print("✨ TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary of fixes:")
    print("• clean_transcript now uses the most recent transcript by default")
    print("• Added option to specify transcript_id in request body")
    print("• Enhanced get_transcript endpoint with metadata")
    print("• Added transcript ordering and identification")
    print("• Fixed transcript ID confusion issue")
    print("\n🎯 The API now correctly handles multiple transcripts per audio file!")
