#!/usr/bin/env python3
"""
Test script to demonstrate enhanced address extraction capabilities
"""

import re
from transformers import AutoModelForTokenClassification, AutoTokenizer, pipeline

class AddressExtractor:
    """Test implementation of enhanced address extraction"""
    
    def __init__(self):
        # Initialize IndicNER pipeline
        self.ner_pipeline = None
        try:
            model_name = "ai4bharat/IndicNER"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForTokenClassification.from_pretrained(model_name)
            self.ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer)
            print("✅ IndicNER model loaded successfully")
        except Exception as e:
            print(f"⚠️  Warning: Could not load IndicNER model: {str(e)}")
            print("   Address extraction will use regex patterns only")
    
    def extract_full_address_with_indicner(self, text):
        """Extract comprehensive address information using IndicNER and regex patterns"""
        extracted_info = {
            "Full Addresses": [],
            "Address": [], 
            "House Number": [],
            "Pincode": []
        }
        
        # Enhanced regex patterns for Indian addresses
        full_address_pattern = re.compile(r"\b\d+\s+[\w\s,-]+?\d{6}\b")  # Complete address with pincode
        house_number_pattern = re.compile(r"\b\d+\s+[\w\s]+(?:road|street|lane|avenue|st|rd|ln|ave|main|cross)\b", re.IGNORECASE)
        pincode_pattern = re.compile(r"\b\d{6}\b")  # 6-digit PIN codes
        
        # Extract full addresses using regex
        full_addresses = full_address_pattern.findall(text)
        extracted_info["Full Addresses"].extend(full_addresses)
        
        # Extract house numbers using enhanced regex
        house_numbers = house_number_pattern.findall(text)
        extracted_info["House Number"].extend(house_numbers)
        
        # Extract PIN codes
        pincodes = pincode_pattern.findall(text)
        extracted_info["Pincode"].extend(pincodes)
        
        # Use IndicNER if available for location entities
        if self.ner_pipeline:
            try:
                entities = self.ner_pipeline(text)
                location_entities = []
                
                for entity in entities:
                    if entity["entity"] in ["B-LOC", "I-LOC", "B-ADDRESS", "I-ADDRESS"]:
                        location_entities.append(entity["word"])
                        extracted_info["Address"].append(entity["word"].strip())
                
                # If regex didn't capture full address, try constructing from NER entities
                if not full_addresses and location_entities:
                    # Group consecutive location entities
                    constructed_address = " ".join(location_entities)
                    if len(constructed_address) > 10:  # Only if substantial
                        extracted_info["Full Addresses"].append(constructed_address)
                        
            except Exception as e:
                print(f"IndicNER processing error: {str(e)}")
        
        # Additional pattern matching for Indian address formats
        indian_address_patterns = [
            r"\b\d+[/-]\d+\s+[\w\s,]+(?:nagar|colony|layout|extension|phase|sector)\b",  # Plot/house numbers
            r"\b(?:flat|apartment|apt|unit)\s*[#]?\s*\d+\s*[,]?\s*[\w\s]+\b",  # Apartment numbers
            r"\b\d+\s*[,]?\s*\d+(?:st|nd|rd|th)?\s+(?:main|cross|stage|phase)\b",  # Main/cross references
            r"\b(?:near|opp|opposite)\s+[\w\s]+(?:temple|school|hospital|mall|station)\b"  # Landmarks
        ]
        
        for pattern in indian_address_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            extracted_info["Address"].extend(matches)
        
        # Clean and deduplicate results
        for key in extracted_info:
            extracted_info[key] = list(set(item.strip() for item in extracted_info[key] if item.strip()))
        
        return extracted_info

def test_address_extraction():
    """Test enhanced address extraction with various Indian address formats"""
    print("=" * 80)
    print("TESTING ENHANCED ADDRESS EXTRACTION")
    print("=" * 80)
    
    extractor = AddressExtractor()
    
    # Test cases with various Indian address formats
    test_cases = [
        {
            "name": "Complete Indian Address with PIN",
            "text": "I live at 123 MG Road, Koramangala, Bengaluru, Karnataka 560034",
            "expected": ["house number", "street", "area", "city", "pincode"]
        },
        {
            "name": "Apartment Address",
            "text": "My address is Flat 4B, Prestige Apartments, 456 Brigade Road, Bengaluru 560025",
            "expected": ["flat number", "building name", "street", "pincode"]
        },
        {
            "name": "Address with Landmarks",
            "text": "I stay near Lalbagh Botanical Garden, 789 South End Circle, Jayanagar, Bengaluru",
            "expected": ["landmark", "area", "city"]
        },
        {
            "name": "Plot Number Format",
            "text": "Plot number 12/34, Whitefield Main Road, EPIP Zone, Whitefield, Bengaluru 560066",
            "expected": ["plot number", "main road", "zone", "area", "pincode"]
        },
        {
            "name": "Speech-to-Text Address",
            "text": "I live at one two three main street apartment four B near the metro station in Bengaluru five six zero zero three four",
            "expected": ["house number", "street", "apartment", "landmark", "city", "pincode"]
        },
        {
            "name": "Complex Address with Multiple Components",
            "text": "My office is at Unit 567, 8th Floor, Tech Park Building, Electronic City Phase 1, Hosur Road, Bengaluru, Karnataka 560100",
            "expected": ["unit", "floor", "building", "phase", "road", "city", "state", "pincode"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🏠 Test {i}: {test_case['name']}")
        print(f"Input: {test_case['text']}")
        print("-" * 60)
        
        # Extract address information
        result = extractor.extract_full_address_with_indicner(test_case['text'])
        
        # Display results
        for category, items in result.items():
            if items:
                print(f"  {category}: {items}")
        
        # Check if we found substantial address information
        total_items = sum(len(items) for items in result.values())
        if total_items > 0:
            print(f"  ✅ Found {total_items} address components")
        else:
            print(f"  ❌ No address components found")
        
        print("-" * 60)

def test_regex_patterns():
    """Test individual regex patterns for address components"""
    print("\n" + "=" * 80)
    print("TESTING INDIVIDUAL REGEX PATTERNS")
    print("=" * 80)
    
    patterns = {
        "House Numbers": r"\b\d+\s+[\w\s]+(?:road|street|lane|avenue|st|rd|ln|ave|main|cross)\b",
        "PIN Codes": r"\b\d{6}\b",
        "Plot Numbers": r"\b\d+[/-]\d+\s+[\w\s,]+(?:nagar|colony|layout|extension|phase|sector)\b",
        "Apartments": r"\b(?:flat|apartment|apt|unit)\s*[#]?\s*\d+\s*[,]?\s*[\w\s]+\b",
        "Landmarks": r"\b(?:near|opp|opposite)\s+[\w\s]+(?:temple|school|hospital|mall|station)\b"
    }
    
    test_text = """
    I live at 123 MG Road near the metro station. My friend stays at 
    Flat 4B, Prestige Apartments, 456 Brigade Road, Bengaluru 560025.
    The office is at Plot 12/34, Electronic City Phase 1, opposite the tech park.
    """
    
    print(f"Test Text: {test_text.strip()}")
    print("\nPattern Matches:")
    
    for pattern_name, pattern in patterns.items():
        matches = re.findall(pattern, test_text, re.IGNORECASE)
        print(f"  {pattern_name}: {matches if matches else 'No matches'}")

if __name__ == "__main__":
    print("Enhanced Address Extraction Test Suite")
    print("=" * 80)
    
    # Test comprehensive address extraction
    test_address_extraction()
    
    # Test individual regex patterns
    test_regex_patterns()
    
    print("\n" + "=" * 80)
    print("ADDRESS EXTRACTION TEST COMPLETED")
    print("=" * 80)
    print("\n📋 Summary:")
    print("- Enhanced regex patterns for Indian address formats")
    print("- IndicNER integration for location entity recognition")
    print("- Support for apartments, plots, landmarks, and PIN codes")
    print("- Comprehensive address component extraction")
    print("- Fallback mechanisms for robust extraction")
