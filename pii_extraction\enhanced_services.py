import spacy
import re
from django.conf import settings
from .models import PIIData
from audio_processing.models import Transcript, Dialogue
import json
from transformers import AutoModelForTokenClassification, AutoTokenizer, pipeline
import os

class EnhancedPIIExtractor:
    def __init__(self ):
        """Initialize enhanced PII extractor with spaCy and IndicNER models"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            self.nlp = None
        
        # Initialize IndicNER components
        self.indic_ner_pipeline = None
        try:
            model_name = "ai4bharat/IndicNER"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForTokenClassification.from_pretrained(model_name)
            self.indic_ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer)
        except Exception as e:
            print(f"Warning: Could not initialize IndicNER pipeline: {str(e)}")

        # Enhanced regex patterns for PII extraction
        self.patterns = {
            'phone': [
                r'\b(?:\+?1[-.]?)?\s*\(?([0-9]{3})\)?[-.\s]*([0-9]{3})[-.\s]*([0-9]{4})\b',
                r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b',
                r'\b\(\d{3}\)\s?\d{3}[-.\s]?\d{4}\b'
            ],
            'email': [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ],
            'date': [
                r'\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'\
                r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'\
                r'Dec(?:ember)?)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}\b',
                r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b',
                r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b'
            ],
            'time': [
                r'\b(?:1[0-2]|0?[1-9])(?:[:][0-5][0-9])?\s*(?:am|pm|AM|PM)\b',
                r'\b(?:2[0-3]|[01]?[0-9]):[0-5][0-9]\b'
            ],
            'address': [
                r'\b\d+\s+[\w\s,-]+?\d{5}(?:-\d{4})?\b',  # US addresses
                r'\b\d+\s+[\w\s,-]+?\d{6}\b'  # Indian addresses
            ],
            'ssn': [
                r'\b\d{3}-\d{2}-\d{4}\b',
                r'\b\d{9}\b'
            ],
            'credit_card': [
                r'\b(?:\d{4}[-\s]?){3}\d{4}\b'
            ]
        }
        
        # Regex patterns for house numbers and PIN codes from provided pii.py
        self.house_number_pattern = re.compile(r"\b\d+\s+\w+.*?\b")
        self.pincode_pattern = re.compile(r"\b\d{6}\b")

    def extract_names(self, text):
        """Extract person names from text using spaCy NER"""
        if not self.nlp:
            return self._extract_names_regex(text)
        
        doc = self.nlp(text)
        names = []
        
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                # Clean and validate name
                name = ent.text.strip()
                if len(name) > 1 and not name.isdigit():
                    names.append(name)
        
        return names if names else ["Not Found"]
    
    def _extract_names_regex(self, text):
        """Fallback name extraction using regex patterns"""
        # Simple pattern for names (capitalized words)
        name_pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        potential_names = re.findall(name_pattern, text)
        
        # Filter out common words that aren't names
        common_words = {'The', 'This', 'That', 'And', 'But', 'For', 'With', 'From'}
        names = [name for name in potential_names if name not in common_words]
        
        return names[:5] if names else ["Not Found"]  # Limit to 5 names
    
    def extract_addresses(self, text):
        """Extract addresses from text using multiple methods including IndicNER"""
        addresses = []
        
        # Use spaCy NER if available
        if self.nlp:
            doc = self.nlp(text)
            for ent in doc.ents:
                if ent.label_ in ["GPE", "LOC", "FAC"]:
                    addresses.append(ent.text.strip())
        
        # Use IndicNER if available
        if self.indic_ner_pipeline:
            entities = self.indic_ner_pipeline(text)
            for entity in entities:
                if entity["entity"] in ["B-LOC", "I-LOC", "B-ADDRESS", "I-ADDRESS"]:
                    addresses.append(entity["word"].strip())
        
        # Use regex patterns for full addresses, house numbers, and pincodes
        for pattern in self.patterns['address']:
            matches = re.findall(pattern, text)
            addresses.extend(matches)
        
        house_numbers = self.house_number_pattern.findall(text)
        addresses.extend(house_numbers)
        
        pincodes = self.pincode_pattern.findall(text)
        addresses.extend(pincodes)

        # Remove duplicates and clean
        unique_addresses = list(set(addr.strip() for addr in addresses if len(addr.strip()) > 5))
        
        return unique_addresses if unique_addresses else ["Not Found"]
    
    def extract_by_pattern(self, text, pattern_type):
        """Extract data using regex patterns"""
        results = []
        
        if pattern_type in self.patterns:
            for pattern in self.patterns[pattern_type]:
                matches = re.findall(pattern, text)
                if pattern_type == 'phone' and isinstance(matches[0], tuple) if matches else False:
                    # Handle phone number tuples
                    results.extend([f"{m[0]}-{m[1]}-{m[2]}" for m in matches])
                else:
                    results.extend(matches)
        
        # Remove duplicates
        unique_results = list(set(results))
        return unique_results if unique_results else ["Not Found"]
    
    def extract_pii_from_transcript(self, transcript_id):
        """
        Extract comprehensive PII from a transcript
        """
        try:
            transcript = Transcript.objects.get(id=transcript_id)
            
            # Get full transcript text
            full_text = transcript.text
            
            # Also extract from individual dialogues for better context
            dialogues = transcript.dialogues.all()
            dialogue_texts = [d.text for d in dialogues]
            combined_text = full_text + " " + " ".join(dialogue_texts)
            
            # Extract all PII types
            extracted_data = {
                "names": self.extract_names(combined_text),
                "phone_numbers": self.extract_by_pattern(combined_text, 'phone'),
                "emails": self.extract_by_pattern(combined_text, 'email'),
                "addresses": self.extract_addresses(combined_text),
                "appointment_dates": self.extract_by_pattern(combined_text, 'date'),
                "appointment_times": self.extract_by_pattern(combined_text, 'time'),
                "ssn": self.extract_by_pattern(combined_text, 'ssn'),
                "credit_cards": self.extract_by_pattern(combined_text, 'credit_card')
            }
            
            # Create or update PII data record
            pii_data, created = PIIData.objects.get_or_create(
                transcript=transcript,
                defaults=extracted_data
            )
            
            if not created:
                # Update existing record
                for key, value in extracted_data.items():
                    setattr(pii_data, key, value)
                pii_data.save()
            
            return pii_data
            
        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error extracting PII: {str(e)}")

class EnhancedTranscriptCleaner:
    """Enhanced transcript cleaning and processing"""

    def __init__(self):
        self.filler_words = [
            'um', 'uh', 'er', 'ah', 'like', 'you know', 'sort of', 'kind of',
            'basically', 'actually', 'literally', 'obviously', 'definitely'
        ]

        self.noise_patterns = [
            r'\[.*?\]',  # Remove bracketed content like [NOISE]
            r'\(.*?\)',  # Remove parenthetical content
            r'<.*?>',    # Remove HTML-like tags
            r'\*.*?\*'   # Remove asterisk-wrapped content
        ]

        # Common email domain typos and their corrections
        self.email_typos = {
            "gemal": "gmail",
            "yahooo": "yahoo",
            "gamil": "gmail",
            "gnail": "gmail",
            "hotmal": "hotmail",
            "outlookt": "outlook",
            "protonmial": "protonmail",
        }

    def correct_email_format(self, text):
        """Replace 'at the rate' with '@' in email contexts using regex"""
        # Regex pattern to detect email-like patterns with "at the rate"
        email_pattern = r"(\b[\w\.-]+)\s+at\s+the\s+rate\s+([\w\.-]+\.\w+\b)"
        # Extended pattern to catch common domains like "gmail" and handle errors like "dred"
        extended_email_pattern = r"(\b[\w\.-]+)\s+at\s+(dred|[\w]+)\s*([\w\.-]+\.\w+\b)"

        # Replace with correct "@" format
        corrected_text = re.sub(email_pattern, r"\1@\2", text)
        corrected_text = re.sub(extended_email_pattern, r"\1@\3", corrected_text)

        # Correct common domain typos
        for typo, correct_domain in self.email_typos.items():
            typo_pattern = r"\b" + re.escape(typo) + r"\b"
            corrected_text = re.sub(typo_pattern, correct_domain, corrected_text)

        return corrected_text

    def correct_phone_number_format(self, text):
        """Handle phone numbers that might be split across segments"""
        # Regex pattern for a 10-digit phone number split into two parts
        phone_number_pattern = r"(\d{5})\s+(\d{5})"
        # Combine two parts of the phone number into one
        corrected_text = re.sub(phone_number_pattern, r"\1\2", text)
        return corrected_text

    def correct_text(self, text):
        """Function to clean and correct the transcription"""
        # Step 1: Correct "at the rate" for email contexts
        text = self.correct_email_format(text)

        # Step 2: Correct phone number format
        text = self.correct_phone_number_format(text)

        return text
    
    def clean_transcript(self, text):
        """
        Comprehensive transcript cleaning
        """
        if not text:
            return ""

        # Apply email and phone number corrections first
        text = self.correct_text(text)

        # Remove noise patterns
        for pattern in self.noise_patterns:
            text = re.sub(pattern, '', text)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove filler words (case insensitive)
        for filler in self.filler_words:
            pattern = r'\b' + re.escape(filler) + r'\b'
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Fix multiple spaces
        text = re.sub(r'\s+', ' ', text)

        # Fix punctuation spacing
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s*', r'\1 ', text)

        # Capitalize first letter of sentences
        text = re.sub(r'(^|[.!?]\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), text)

        # Remove leading/trailing whitespace
        text = text.strip()

        return text
    
    def process_dialogue_text(self, dialogue_text):
        """Process individual dialogue text"""
        # Apply email and phone number corrections first
        dialogue_text = self.correct_text(dialogue_text)

        # Then apply standard cleaning
        cleaned = self.clean_transcript(dialogue_text)

        # Additional processing for dialogue
        # Remove speaker indicators if present
        cleaned = re.sub(r'^(Speaker\s*\d+|Agent|Customer|Broker):\s*', '', cleaned, flags=re.IGNORECASE)

        return cleaned
    
    def clean_transcript_dialogues(self, transcript_id):
        """
        Clean all dialogues in a transcript
        """
        try:
            transcript = Transcript.objects.get(id=transcript_id)
            dialogues = transcript.dialogues.all()
            
            for dialogue in dialogues:
                cleaned_text = self.process_dialogue_text(dialogue.text)
                dialogue.text = cleaned_text
                dialogue.save()
            
            # Also clean the main transcript text
            cleaned_transcript = self.clean_transcript(transcript.text)
            transcript.text = cleaned_transcript
            transcript.save()
            
            return transcript
            
        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error cleaning transcript: {str(e)}")

class PIIValidator:
    """Validate and score confidence of extracted PII"""
    
    def __init__(self):
        self.validation_patterns = {
            'phone': r'^\d{3}-\d{3}-\d{4}$',
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'ssn': r'^\d{3}-\d{2}-\d{4}$'
        }
    
    def validate_pii_data(self, pii_data):
        """
        Validate extracted PII data and assign confidence scores
        """
        validation_results = {}
        
        for field_name, field_value in pii_data.items():
            if field_name in ['names', 'phone_numbers', 'emails', 'addresses', 
                             'appointment_dates', 'appointment_times']:
                
                if isinstance(field_value, list) and field_value != ["Not Found"]:
                    validated_items = []
                    
                    for item in field_value:
                        confidence = self._calculate_confidence(field_name, item)
                        validated_items.append({
                            'value': item,
                            'confidence': confidence,
                            'valid': confidence > 0.5
                        })
                    
                    validation_results[field_name] = validated_items
                else:
                    validation_results[field_name] = []
        
        return validation_results
    
    def _calculate_confidence(self, field_type, value):
        """
        Calculate confidence score for a PII value
        """
        if not value or value == "Not Found":
            return 0.0
        
        # Basic confidence calculation
        confidence = 0.5  # Base confidence
        
        if field_type == 'phone_numbers':
            if re.match(self.validation_patterns['phone'], value):
                confidence = 0.9
            elif len(value) >= 10:
                confidence = 0.7
        
        elif field_type == 'emails':
            if re.match(self.validation_patterns['email'], value):
                confidence = 0.9
            elif '@' in value and '.' in value:
                confidence = 0.7
        
        elif field_type == 'names':
            if len(value.split()) >= 2:  # First and last name
                confidence = 0.8
            elif value.istitle():  # Properly capitalized
                confidence = 0.7
        
        elif field_type == 'addresses':
            if len(value) > 20 and any(char.isdigit() for char in value):
                confidence = 0.8
        
        return min(confidence, 1.0)
