import spacy
import re
import os
import json
from datetime import datetime, timedelta
from django.conf import settings
from .models import PIIData
from audio_processing.models import Transcript, Dialogue
from transformers import AutoModelForTokenClassification, AutoTokenizer, pipeline
from groq import Groq

class GroqPIIExtractor:
    """Advanced PII extraction using Groq LLM for better accuracy"""

    def __init__(self, api_key=None):
        self.api_key = api_key or os.getenv('GROQ_API_KEY')
        if not self.api_key:
            raise ValueError("GROQ_API_KEY is required")

        self.client = Groq(api_key=self.api_key)
        self.model_name = "mixtral-8x7b-32768"

        # Define PII extraction prompts
        self.pii_prompts = {
            "comprehensive_extraction": """
            Extract all personally identifiable information (PII) from the following transcript.
            Return the information in JSON format with these exact keys:
            - "names": List of full names mentioned
            - "phone_numbers": List of phone numbers (format as clean numbers)
            - "emails": List of email addresses
            - "addresses": List of complete physical addresses (include house numbers, street names, areas, cities, PIN codes)
            - "appointment_dates": List of dates mentioned
            - "appointment_times": List of times mentioned
            - "organizations": List of company/organization names
            - "identification_numbers": List of any ID numbers (SSN, account numbers, etc.)

            Important:
            - Only extract information that is clearly stated
            - For phone numbers, clean up any speech-to-text errors
            - For emails, fix common transcription errors like "at the rate" -> "@"
            - For addresses, combine house numbers, street names, areas, and PIN codes into complete addresses
            - For dates, convert relative dates like "next Monday" to actual dates
            - Return empty lists for categories with no information

            Transcript:
            "{transcript}"

            Return only valid JSON:
            """,

            "address_extraction": """
            Extract and structure address information from the following transcript.
            Focus on Indian address formats including:
            - House/flat numbers
            - Street names and areas
            - Landmarks and nearby locations
            - PIN codes
            - City and state information

            Return JSON with:
            {{
                "complete_addresses": ["full formatted addresses"],
                "house_numbers": ["house/flat numbers"],
                "street_areas": ["street names and areas"],
                "landmarks": ["nearby landmarks"],
                "pincodes": ["6-digit PIN codes"],
                "cities": ["city names"]
            }}

            Transcript: "{transcript}"
            """,

            "contact_validation": """
            Validate and clean the following contact information extracted from speech-to-text:

            Phone numbers: {phone_numbers}
            Email addresses: {emails}

            Fix common speech-to-text errors:
            - "at the rate" or "at dred" -> "@"
            - Split phone numbers should be combined
            - Remove filler words and correct domain typos

            Return cleaned contact info in JSON format:
            {{"phone_numbers": [], "emails": []}}
            """,

            "date_time_extraction": """
            Extract and normalize all dates and times from this transcript.
            Convert relative dates (like "next Monday", "tomorrow") to actual dates.
            Today's date is: {today_date}

            Transcript: "{transcript}"

            Return JSON with:
            {{"appointment_dates": ["YYYY-MM-DD"], "appointment_times": ["HH:MM AM/PM"]}}
            """
        }

    def extract_pii_with_groq(self, transcript_text):
        """Extract PII using Groq LLM with comprehensive prompts"""
        try:
            # Main PII extraction
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{
                    "role": "user",
                    "content": self.pii_prompts["comprehensive_extraction"].format(
                        transcript=transcript_text
                    )
                }],
                temperature=0.1  # Low temperature for consistent extraction
            )

            # Parse JSON response
            pii_data = self._parse_json_response(response.choices[0].message.content)

            # Additional validation and cleaning for contact info
            if pii_data.get("phone_numbers") or pii_data.get("emails"):
                cleaned_contacts = self._validate_contacts(
                    pii_data.get("phone_numbers", []),
                    pii_data.get("emails", [])
                )
                pii_data.update(cleaned_contacts)

            # Enhanced date/time extraction
            date_time_data = self._extract_dates_times(transcript_text)
            if date_time_data:
                pii_data.update(date_time_data)

            # Enhanced address extraction if addresses are missing or incomplete
            if not pii_data.get("addresses") or len(pii_data.get("addresses", [])) == 0:
                address_data = self._extract_addresses_detailed(transcript_text)
                if address_data and address_data.get("complete_addresses"):
                    pii_data["addresses"] = address_data["complete_addresses"]

            return pii_data

        except Exception as e:
            print(f"Error in Groq PII extraction: {str(e)}")
            return self._get_empty_pii_structure()

    def _parse_json_response(self, response_text):
        """Parse JSON response from Groq, handling potential formatting issues"""
        try:
            # Clean the response text
            response_text = response_text.strip()

            # Find JSON content between ```json and ``` or just raw JSON
            if "```json" in response_text:
                start = response_text.find("```json") + 7
                end = response_text.find("```", start)
                json_text = response_text[start:end].strip()
            elif response_text.startswith("{") and response_text.endswith("}"):
                json_text = response_text
            else:
                # Try to find JSON-like content
                import re
                json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
                json_text = json_match.group(0) if json_match else "{}"

            return json.loads(json_text)

        except json.JSONDecodeError as e:
            print(f"JSON parsing error: {str(e)}")
            return self._get_empty_pii_structure()

    def _validate_contacts(self, phone_numbers, emails):
        """Validate and clean contact information using Groq"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{
                    "role": "user",
                    "content": self.pii_prompts["contact_validation"].format(
                        phone_numbers=phone_numbers,
                        emails=emails
                    )
                }],
                temperature=0.1
            )

            return self._parse_json_response(response.choices[0].message.content)

        except Exception as e:
            print(f"Error validating contacts: {str(e)}")
            return {"phone_numbers": phone_numbers, "emails": emails}

    def _extract_dates_times(self, transcript_text):
        """Extract and normalize dates and times"""
        try:
            today_date = datetime.now().strftime("%Y-%m-%d")

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{
                    "role": "user",
                    "content": self.pii_prompts["date_time_extraction"].format(
                        transcript=transcript_text,
                        today_date=today_date
                    )
                }],
                temperature=0.1
            )

            return self._parse_json_response(response.choices[0].message.content)

        except Exception as e:
            print(f"Error extracting dates/times: {str(e)}")
            return {"appointment_dates": [], "appointment_times": []}

    def _extract_addresses_detailed(self, transcript_text):
        """Extract detailed address information using Groq"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{
                    "role": "user",
                    "content": self.pii_prompts["address_extraction"].format(
                        transcript=transcript_text
                    )
                }],
                temperature=0.1
            )

            return self._parse_json_response(response.choices[0].message.content)

        except Exception as e:
            print(f"Error extracting detailed addresses: {str(e)}")
            return {"complete_addresses": [], "house_numbers": [], "street_areas": [],
                   "landmarks": [], "pincodes": [], "cities": []}

    def _get_empty_pii_structure(self):
        """Return empty PII structure"""
        return {
            "names": [],
            "phone_numbers": [],
            "emails": [],
            "addresses": [],
            "appointment_dates": [],
            "appointment_times": [],
            "organizations": [],
            "identification_numbers": []
        }

class EnhancedPIIExtractor:
    def __init__(self, use_groq=True):
        """Initialize enhanced PII extractor with spaCy, IndicNER, and Groq models"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            self.nlp = None

        # Initialize IndicNER components
        self.indic_ner_pipeline = None
        try:
            model_name = "ai4bharat/IndicNER"
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = AutoModelForTokenClassification.from_pretrained(model_name)
            self.indic_ner_pipeline = pipeline("ner", model=model, tokenizer=tokenizer)
        except Exception as e:
            print(f"Warning: Could not initialize IndicNER pipeline: {str(e)}")

        # Initialize Groq PII extractor
        self.groq_extractor = None
        self.use_groq = use_groq
        if use_groq:
            try:
                self.groq_extractor = GroqPIIExtractor()
            except Exception as e:
                print(f"Warning: Could not initialize Groq PII extractor: {str(e)}")
                self.use_groq = False

        # Enhanced regex patterns for PII extraction
        self.patterns = {
            'phone': [
                r'\b(?:\+?1[-.]?)?\s*\(?([0-9]{3})\)?[-.\s]*([0-9]{3})[-.\s]*([0-9]{4})\b',
                r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b',
                r'\b\(\d{3}\)\s?\d{3}[-.\s]?\d{4}\b'
            ],
            'email': [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            ],
            'date': [
                r'\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'\
                r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'\
                r'Dec(?:ember)?)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}\b',
                r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b',
                r'\b\d{4}[-/]\d{1,2}[-/]\d{1,2}\b'
            ],
            'time': [
                r'\b(?:1[0-2]|0?[1-9])(?:[:][0-5][0-9])?\s*(?:am|pm|AM|PM)\b',
                r'\b(?:2[0-3]|[01]?[0-9]):[0-5][0-9]\b'
            ],
            'address': [
                r'\b\d+\s+[\w\s,-]+?\d{5}(?:-\d{4})?\b',  # US addresses
                r'\b\d+\s+[\w\s,-]+?\d{6}\b'  # Indian addresses
            ],
            'ssn': [
                r'\b\d{3}-\d{2}-\d{4}\b',
                r'\b\d{9}\b'
            ],
            'credit_card': [
                r'\b(?:\d{4}[-\s]?){3}\d{4}\b'
            ]
        }
        
        # Regex patterns for house numbers and PIN codes from provided pii.py
        self.house_number_pattern = re.compile(r"\b\d+\s+\w+.*?\b")
        self.pincode_pattern = re.compile(r"\b\d{6}\b")

    def extract_names(self, text):
        """Extract person names from text using spaCy NER"""
        if not self.nlp:
            return self._extract_names_regex(text)
        
        doc = self.nlp(text)
        names = []
        
        for ent in doc.ents:
            if ent.label_ == "PERSON":
                # Clean and validate name
                name = ent.text.strip()
                if len(name) > 1 and not name.isdigit():
                    names.append(name)
        
        return names if names else ["Not Found"]
    
    def _extract_names_regex(self, text):
        """Fallback name extraction using regex patterns"""
        # Simple pattern for names (capitalized words)
        name_pattern = r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        potential_names = re.findall(name_pattern, text)
        
        # Filter out common words that aren't names
        common_words = {'The', 'This', 'That', 'And', 'But', 'For', 'With', 'From'}
        names = [name for name in potential_names if name not in common_words]
        
        return names[:5] if names else ["Not Found"]  # Limit to 5 names
    
    def extract_addresses(self, text):
        """Enhanced address extraction using multiple methods including IndicNER"""
        addresses = []

        # Use comprehensive address extraction
        address_data = self.extract_full_address_with_indicner(text)

        # Combine all address components
        if address_data.get("Full Addresses"):
            addresses.extend(address_data["Full Addresses"])

        if address_data.get("Address"):
            addresses.extend(address_data["Address"])

        if address_data.get("House Number"):
            addresses.extend(address_data["House Number"])

        if address_data.get("Pincode"):
            addresses.extend(address_data["Pincode"])

        # Use spaCy NER if available as fallback
        if self.nlp and not addresses:
            doc = self.nlp(text)
            for ent in doc.ents:
                if ent.label_ in ["GPE", "LOC", "FAC"]:
                    addresses.append(ent.text.strip())

        # Use traditional regex patterns as additional fallback
        if not addresses:
            for pattern in self.patterns['address']:
                matches = re.findall(pattern, text)
                addresses.extend(matches)

            house_numbers = self.house_number_pattern.findall(text)
            addresses.extend(house_numbers)

            pincodes = self.pincode_pattern.findall(text)
            addresses.extend(pincodes)

        # Remove duplicates and clean
        unique_addresses = list(set(addr.strip() for addr in addresses if len(addr.strip()) > 2))

        return unique_addresses if unique_addresses else ["Not Found"]

    def extract_full_address_with_indicner(self, text):
        """Extract comprehensive address information using IndicNER and regex patterns"""
        extracted_info = {
            "Full Addresses": [],
            "Address": [],
            "House Number": [],
            "Pincode": []
        }

        # Enhanced regex patterns for Indian addresses
        full_address_pattern = re.compile(r"\b\d+\s+[\w\s,-]+?\d{6}\b")  # Complete address with pincode
        house_number_pattern = re.compile(r"\b\d+\s+[\w\s]+(?:road|street|lane|avenue|st|rd|ln|ave|main|cross)\b", re.IGNORECASE)
        pincode_pattern = re.compile(r"\b\d{6}\b")  # 6-digit PIN codes

        # Extract full addresses using regex
        full_addresses = full_address_pattern.findall(text)
        extracted_info["Full Addresses"].extend(full_addresses)

        # Extract house numbers using enhanced regex
        house_numbers = house_number_pattern.findall(text)
        extracted_info["House Number"].extend(house_numbers)

        # Extract PIN codes
        pincodes = pincode_pattern.findall(text)
        extracted_info["Pincode"].extend(pincodes)

        # Use IndicNER if available for location entities
        if self.indic_ner_pipeline:
            try:
                entities = self.indic_ner_pipeline(text)
                location_entities = []

                for entity in entities:
                    if entity["entity"] in ["B-LOC", "I-LOC", "B-ADDRESS", "I-ADDRESS"]:
                        location_entities.append(entity["word"])
                        extracted_info["Address"].append(entity["word"].strip())

                # If regex didn't capture full address, try constructing from NER entities
                if not full_addresses and location_entities:
                    # Group consecutive location entities
                    constructed_address = " ".join(location_entities)
                    if len(constructed_address) > 10:  # Only if substantial
                        extracted_info["Full Addresses"].append(constructed_address)

            except Exception as e:
                print(f"IndicNER processing error: {str(e)}")

        # Additional pattern matching for Indian address formats
        indian_address_patterns = [
            r"\b\d+[/-]\d+\s+[\w\s,]+(?:nagar|colony|layout|extension|phase|sector)\b",  # Plot/house numbers
            r"\b(?:flat|apartment|apt|unit)\s*[#]?\s*\d+\s*[,]?\s*[\w\s]+\b",  # Apartment numbers
            r"\b\d+\s*[,]?\s*\d+(?:st|nd|rd|th)?\s+(?:main|cross|stage|phase)\b",  # Main/cross references
            r"\b(?:near|opp|opposite)\s+[\w\s]+(?:temple|school|hospital|mall|station)\b"  # Landmarks
        ]

        for pattern in indian_address_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            extracted_info["Address"].extend(matches)

        # Clean and deduplicate results
        for key in extracted_info:
            extracted_info[key] = list(set(item.strip() for item in extracted_info[key] if item.strip()))

        return extracted_info
    
    def extract_by_pattern(self, text, pattern_type):
        """Extract data using regex patterns"""
        results = []
        
        if pattern_type in self.patterns:
            for pattern in self.patterns[pattern_type]:
                matches = re.findall(pattern, text)
                if pattern_type == 'phone' and isinstance(matches[0], tuple) if matches else False:
                    # Handle phone number tuples
                    results.extend([f"{m[0]}-{m[1]}-{m[2]}" for m in matches])
                else:
                    results.extend(matches)
        
        # Remove duplicates
        unique_results = list(set(results))
        return unique_results if unique_results else ["Not Found"]
    
    def extract_pii_from_transcript(self, transcript_id):
        """
        Extract comprehensive PII from a transcript using multiple methods
        """
        try:
            transcript = Transcript.objects.get(id=transcript_id)

            # Get full transcript text
            full_text = transcript.text

            # Also extract from individual dialogues for better context
            dialogues = transcript.dialogues.all()
            dialogue_texts = [d.text for d in dialogues]
            combined_text = full_text + " " + " ".join(dialogue_texts)

            # Initialize with traditional extraction methods
            extracted_data = {
                "names": self.extract_names(combined_text),
                "phone_numbers": self.extract_by_pattern(combined_text, 'phone'),
                "emails": self.extract_by_pattern(combined_text, 'email'),
                "addresses": self.extract_addresses(combined_text),
                "appointment_dates": self.extract_by_pattern(combined_text, 'date'),
                "appointment_times": self.extract_by_pattern(combined_text, 'time'),
                "ssn": self.extract_by_pattern(combined_text, 'ssn'),
                "credit_cards": self.extract_by_pattern(combined_text, 'credit_card')
            }

            # Use Groq for enhanced extraction if available
            if self.use_groq and self.groq_extractor:
                try:
                    groq_data = self.groq_extractor.extract_pii_with_groq(combined_text)

                    # Merge Groq results with traditional results
                    extracted_data = self._merge_pii_results(extracted_data, groq_data)

                except Exception as e:
                    print(f"Groq extraction failed, using traditional methods: {str(e)}")

            # Create or update PII data record
            pii_data, created = PIIData.objects.get_or_create(
                transcript=transcript,
                defaults=extracted_data
            )

            if not created:
                # Update existing record
                for key, value in extracted_data.items():
                    setattr(pii_data, key, value)
                pii_data.save()

            return pii_data

        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error extracting PII: {str(e)}")

    def _merge_pii_results(self, traditional_data, groq_data):
        """Merge results from traditional extraction and Groq extraction"""
        merged_data = {}

        # Define field mappings between traditional and Groq formats
        field_mappings = {
            "names": "names",
            "phone_numbers": "phone_numbers",
            "emails": "emails",
            "addresses": "addresses",
            "appointment_dates": "appointment_dates",
            "appointment_times": "appointment_times",
            "ssn": "identification_numbers",
            "credit_cards": "identification_numbers"
        }

        for trad_field, groq_field in field_mappings.items():
            # Start with traditional results
            trad_results = traditional_data.get(trad_field, [])
            if isinstance(trad_results, str):
                trad_results = [trad_results] if trad_results != "Not Found" else []

            # Add Groq results
            groq_results = groq_data.get(groq_field, [])
            if isinstance(groq_results, str):
                groq_results = [groq_results] if groq_results else []

            # Combine and deduplicate
            combined = list(set(trad_results + groq_results))

            # Filter out "Not Found" entries
            combined = [item for item in combined if item and item != "Not Found"]

            # Set result or default to ["Not Found"]
            merged_data[trad_field] = combined if combined else ["Not Found"]

        # Add any additional Groq fields
        if "organizations" in groq_data:
            merged_data["organizations"] = groq_data["organizations"]

        return merged_data

class EnhancedTranscriptCleaner:
    """Enhanced transcript cleaning and processing"""

    def __init__(self):
        self.filler_words = [
            'um', 'uh', 'er', 'ah', 'like', 'you know', 'sort of', 'kind of',
            'basically', 'actually', 'literally', 'obviously', 'definitely'
        ]

        self.noise_patterns = [
            r'\[.*?\]',  # Remove bracketed content like [NOISE]
            r'\(.*?\)',  # Remove parenthetical content
            r'<.*?>',    # Remove HTML-like tags
            r'\*.*?\*'   # Remove asterisk-wrapped content
        ]

        # Enhanced email domain typos and their corrections
        self.email_typos = {
            "gemal": "gmail",
            "yahooo": "yahoo",
            "gamil": "gmail",
            "gnail": "gmail",
            "hotmal": "hotmail",
            "outlookt": "outlook",
            "protonmial": "protonmail",
            "g mail": "gmail",
            "hot mail": "hotmail",
            "out look": "outlook",
            "ya hoo": "yahoo",
            "i cloud": "icloud",
            "live dot com": "live.com",
            "msn dot com": "msn.com"
        }

        # Common speech-to-text corrections
        self.speech_corrections = {
            "at the rate": "@",
            "at dred": "@",
            "at red": "@",
            "at rate": "@",
            "at symbol": "@",
            "at sign": "@",
            "dot com": ".com",
            "dot org": ".org",
            "dot net": ".net",
            "dot edu": ".edu",
            "dot gov": ".gov",
            "dash": "-",
            "underscore": "_",
            "forward slash": "/",
            "back slash": "\\",
            "open parenthesis": "(",
            "close parenthesis": ")",
            "open bracket": "[",
            "close bracket": "]",
            "hashtag": "#",
            "dollar sign": "$",
            "percent": "%",
            "ampersand": "&",
            "asterisk": "*",
            "plus sign": "+",
            "equals": "=",
            "question mark": "?",
            "exclamation point": "!",
            "semicolon": ";",
            "colon": ":",
            "comma": ",",
            "period": ".",
            "quotation mark": '"',
            "apostrophe": "'",
            "space": " "
        }

        # Phone number patterns and corrections
        self.phone_corrections = {
            "zero": "0", "one": "1", "two": "2", "three": "3", "four": "4",
            "five": "5", "six": "6", "seven": "7", "eight": "8", "nine": "9",
            "oh": "0", "double": "", "triple": ""
        }

    def correct_email_format(self, text):
        """Enhanced email format correction with comprehensive speech-to-text fixes"""
        corrected_text = text

        # Apply general speech corrections first
        for speech_error, correction in self.speech_corrections.items():
            pattern = r"\b" + re.escape(speech_error) + r"\b"
            corrected_text = re.sub(pattern, correction, corrected_text, flags=re.IGNORECASE)

        # Enhanced email patterns
        email_patterns = [
            # Standard "at the rate" patterns
            (r"(\b[\w\.-]+)\s+at\s+the\s+rate\s+([\w\.-]+\.\w+\b)", r"\1@\2"),
            (r"(\b[\w\.-]+)\s+at\s+(dred|red|rate)\s+([\w\.-]+\.\w+\b)", r"\1@\3"),
            # More variations
            (r"(\b[\w\.-]+)\s+at\s+symbol\s+([\w\.-]+\.\w+\b)", r"\1@\2"),
            (r"(\b[\w\.-]+)\s+at\s+sign\s+([\w\.-]+\.\w+\b)", r"\1@\2"),
            (r"(\b[\w\.-]+)\s+@\s+([\w\.-]+)\s+dot\s+(\w+\b)", r"\1@\2.\3"),
            # Handle spaced out emails
            (r"(\b[\w\.-]+)\s+@\s+([\w\.-]+)\s*\.\s*(\w+\b)", r"\1@\2.\3"),
        ]

        for pattern, replacement in email_patterns:
            corrected_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE)

        # Correct domain typos
        for typo, correct_domain in self.email_typos.items():
            typo_pattern = r"\b" + re.escape(typo) + r"\b"
            corrected_text = re.sub(typo_pattern, correct_domain, corrected_text, flags=re.IGNORECASE)

        # Fix common email structure issues
        corrected_text = re.sub(r"(\w+)\s+dot\s+(\w+)\s+at\s+(\w+)\s+dot\s+(\w+)",
                               r"\1.\2@\3.\4", corrected_text, flags=re.IGNORECASE)

        return corrected_text

    def correct_phone_number_format(self, text):
        """Enhanced phone number correction with speech-to-text fixes"""
        corrected_text = text

        # Convert spoken numbers to digits
        for word, digit in self.phone_corrections.items():
            pattern = r"\b" + re.escape(word) + r"\b"
            corrected_text = re.sub(pattern, digit, corrected_text, flags=re.IGNORECASE)

        # Handle various phone number patterns
        phone_patterns = [
            # Split phone numbers
            (r"(\d{5})\s+(\d{5})", r"\1\2"),
            (r"(\d{3})\s+(\d{3})\s+(\d{4})", r"\1\2\3"),
            (r"(\d{3})\s*-\s*(\d{3})\s*-\s*(\d{4})", r"\1-\2-\3"),
            # Parentheses format
            (r"\(\s*(\d{3})\s*\)\s*(\d{3})\s*-?\s*(\d{4})", r"(\1) \2-\3"),
            # Plus format
            (r"\+\s*1\s*(\d{3})\s*(\d{3})\s*(\d{4})", r"+1\1\2\3"),
            # Remove extra spaces in numbers
            (r"(\d)\s+(\d)", r"\1\2"),
        ]

        for pattern, replacement in phone_patterns:
            corrected_text = re.sub(pattern, replacement, corrected_text)

        # Handle spelled out phone numbers
        spelled_patterns = [
            (r"(\d{3})\s*dash\s*(\d{3})\s*dash\s*(\d{4})", r"\1-\2-\3"),
            (r"area\s*code\s*(\d{3})", r"(\1)"),
            (r"extension\s*(\d+)", r"ext. \1"),
            (r"ext\s*(\d+)", r"ext. \1"),
        ]

        for pattern, replacement in spelled_patterns:
            corrected_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE)

        return corrected_text

    def correct_address_format(self, text):
        """Correct common address transcription errors"""
        corrected_text = text

        # Address-specific corrections
        address_corrections = {
            "street": "St", "saint": "St", "avenue": "Ave", "road": "Rd",
            "lane": "Ln", "drive": "Dr", "court": "Ct", "place": "Pl",
            "boulevard": "Blvd", "circle": "Cir", "way": "Way",
            "north": "N", "south": "S", "east": "E", "west": "W",
            "northeast": "NE", "northwest": "NW", "southeast": "SE", "southwest": "SW",
            "apartment": "Apt", "suite": "Ste", "floor": "Fl", "unit": "Unit"
        }

        for word, abbrev in address_corrections.items():
            pattern = r"\b" + re.escape(word) + r"\b"
            corrected_text = re.sub(pattern, abbrev, corrected_text, flags=re.IGNORECASE)

        # Fix ZIP code patterns
        corrected_text = re.sub(r"zip\s*code\s*(\d{5})", r"\1", corrected_text, flags=re.IGNORECASE)
        corrected_text = re.sub(r"postal\s*code\s*(\d{5,6})", r"\1", corrected_text, flags=re.IGNORECASE)

        return corrected_text

    def correct_date_time_format(self, text):
        """Correct date and time transcription errors"""
        corrected_text = text

        # Day corrections
        day_corrections = {
            "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday",
            "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"
        }

        # Month corrections
        month_corrections = {
            "january": "January", "february": "February", "march": "March",
            "april": "April", "may": "May", "june": "June",
            "july": "July", "august": "August", "september": "September",
            "october": "October", "november": "November", "december": "December"
        }

        # Apply corrections
        for word, correct in {**day_corrections, **month_corrections}.items():
            pattern = r"\b" + re.escape(word) + r"\b"
            corrected_text = re.sub(pattern, correct, corrected_text, flags=re.IGNORECASE)

        # Time format corrections
        time_patterns = [
            (r"(\d{1,2})\s*o\s*clock", r"\1:00"),
            (r"(\d{1,2})\s*thirty", r"\1:30"),
            (r"(\d{1,2})\s*fifteen", r"\1:15"),
            (r"(\d{1,2})\s*forty\s*five", r"\1:45"),
            (r"half\s*past\s*(\d{1,2})", r"\1:30"),
            (r"quarter\s*past\s*(\d{1,2})", r"\1:15"),
            (r"quarter\s*to\s*(\d{1,2})", lambda m: f"{int(m.group(1))-1}:45"),
        ]

        for pattern, replacement in time_patterns:
            corrected_text = re.sub(pattern, replacement, corrected_text, flags=re.IGNORECASE)

        return corrected_text

    def correct_text(self, text):
        """Comprehensive function to clean and correct the transcription"""
        # Step 1: Apply general speech corrections
        for speech_error, correction in self.speech_corrections.items():
            pattern = r"\b" + re.escape(speech_error) + r"\b"
            text = re.sub(pattern, correction, text, flags=re.IGNORECASE)

        # Step 2: Correct email formats
        text = self.correct_email_format(text)

        # Step 3: Correct phone number formats
        text = self.correct_phone_number_format(text)

        # Step 4: Correct address formats
        text = self.correct_address_format(text)

        # Step 5: Correct date and time formats
        text = self.correct_date_time_format(text)

        # Step 6: Clean up extra spaces
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text
    
    def clean_transcript(self, text):
        """
        Comprehensive transcript cleaning with enhanced corrections
        """
        if not text:
            return ""

        # Step 1: Apply comprehensive text corrections first
        text = self.correct_text(text)

        # Step 2: Remove noise patterns
        for pattern in self.noise_patterns:
            text = re.sub(pattern, '', text)

        # Step 3: Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Step 4: Remove filler words (case insensitive)
        for filler in self.filler_words:
            pattern = r'\b' + re.escape(filler) + r'\b'
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # Step 5: Fix multiple spaces again after filler word removal
        text = re.sub(r'\s+', ' ', text)

        # Step 6: Fix punctuation spacing
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([,.!?;:])\s*', r'\1 ', text)

        # Step 7: Capitalize first letter of sentences
        text = re.sub(r'(^|[.!?]\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), text)

        # Step 8: Fix common transcription artifacts
        text = re.sub(r'\b(um|uh|er|ah)\b', '', text, flags=re.IGNORECASE)
        text = re.sub(r'\[.*?\]', '', text)  # Remove bracketed content
        text = re.sub(r'\(.*?\)', '', text)  # Remove parenthetical content

        # Step 9: Final cleanup
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()

        return text
    
    def process_dialogue_text(self, dialogue_text):
        """Process individual dialogue text"""
        # Apply email and phone number corrections first
        dialogue_text = self.correct_text(dialogue_text)

        # Then apply standard cleaning
        cleaned = self.clean_transcript(dialogue_text)

        # Additional processing for dialogue
        # Remove speaker indicators if present
        cleaned = re.sub(r'^(Speaker\s*\d+|Agent|Customer|Broker):\s*', '', cleaned, flags=re.IGNORECASE)

        return cleaned
    
    def clean_transcript_dialogues(self, transcript_id):
        """
        Clean all dialogues in a transcript
        """
        try:
            transcript = Transcript.objects.get(id=transcript_id)
            dialogues = transcript.dialogues.all()
            
            for dialogue in dialogues:
                cleaned_text = self.process_dialogue_text(dialogue.text)
                dialogue.text = cleaned_text
                dialogue.save()
            
            # Also clean the main transcript text
            cleaned_transcript = self.clean_transcript(transcript.text)
            transcript.text = cleaned_transcript
            transcript.save()
            
            return transcript
            
        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error cleaning transcript: {str(e)}")

class PIIValidator:
    """Validate and score confidence of extracted PII"""
    
    def __init__(self):
        self.validation_patterns = {
            'phone': r'^\d{3}-\d{3}-\d{4}$',
            'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
            'ssn': r'^\d{3}-\d{2}-\d{4}$'
        }
    
    def validate_pii_data(self, pii_data):
        """
        Validate extracted PII data and assign confidence scores
        """
        validation_results = {}
        
        for field_name, field_value in pii_data.items():
            if field_name in ['names', 'phone_numbers', 'emails', 'addresses', 
                             'appointment_dates', 'appointment_times']:
                
                if isinstance(field_value, list) and field_value != ["Not Found"]:
                    validated_items = []
                    
                    for item in field_value:
                        confidence = self._calculate_confidence(field_name, item)
                        validated_items.append({
                            'value': item,
                            'confidence': confidence,
                            'valid': confidence > 0.5
                        })
                    
                    validation_results[field_name] = validated_items
                else:
                    validation_results[field_name] = []
        
        return validation_results
    
    def _calculate_confidence(self, field_type, value):
        """
        Calculate confidence score for a PII value
        """
        if not value or value == "Not Found":
            return 0.0
        
        # Basic confidence calculation
        confidence = 0.5  # Base confidence
        
        if field_type == 'phone_numbers':
            if re.match(self.validation_patterns['phone'], value):
                confidence = 0.9
            elif len(value) >= 10:
                confidence = 0.7
        
        elif field_type == 'emails':
            if re.match(self.validation_patterns['email'], value):
                confidence = 0.9
            elif '@' in value and '.' in value:
                confidence = 0.7
        
        elif field_type == 'names':
            if len(value.split()) >= 2:  # First and last name
                confidence = 0.8
            elif value.istitle():  # Properly capitalized
                confidence = 0.7
        
        elif field_type == 'addresses':
            if len(value) > 20 and any(char.isdigit() for char in value):
                confidence = 0.8
        
        return min(confidence, 1.0)
