# Generated by Django 5.2.2 on 2025-06-07 16:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('audio_processing', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='IRAReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_text', models.TextField()),
                ('summary', models.TextField()),
                ('sentiment_score', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('report_file', models.FileField(blank=True, null=True, upload_to='reports/')),
                ('transcript', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='ira_report', to='audio_processing.transcript')),
            ],
        ),
    ]
