#!/usr/bin/env python3
"""
Test that calendar events use live data from Google Sheets instead of internal database
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_live_sheet_data_usage():
    """Test that calendar events read live data from Google Sheets"""
    print("📊 TESTING LIVE GOOGLE SHEETS DATA USAGE")
    print("=" * 60)
    
    try:
        # Step 1: Get latest PII and save to sheets
        print("📄 Step 1: Getting latest transcript and extracting PII...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    print(f"✅ PII extracted - ID: {pii_id}")
                    print(f"   Original Email: {pii_data.get('emails', [])}")
                    print(f"   Original Phone: {pii_data.get('phone_numbers', [])}")
                    
                    # Save to Google Sheets
                    print(f"\n📊 Step 2: Saving to Google Sheets...")
                    response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
                    
                    if response.status_code == 200:
                        sheet_data = response.json()
                        sheet_entry_id = sheet_data.get('sheet_entry_id')
                        row_number = sheet_data.get('row_number')
                        
                        print(f"✅ Saved to sheets - Entry ID: {sheet_entry_id}")
                        print(f"   Row number: {row_number}")
                        
                        # Step 3: Show what the API will do
                        print(f"\n📝 Step 3: What happens next...")
                        print(f"   🔄 OLD BEHAVIOR: API would read from internal database")
                        print(f"      - Email: {pii_data.get('emails', [])[0] if pii_data.get('emails') else 'N/A'}")
                        print(f"      - Phone: {pii_data.get('phone_numbers', [])[0] if pii_data.get('phone_numbers') else 'N/A'}")
                        print(f"   ✅ NEW BEHAVIOR: API will read from Google Sheets row {row_number}")
                        print(f"      - Will use whatever email/phone you put in the sheet")
                        print(f"      - Live data from sheets takes priority")
                        
                        # Step 4: Create calendar event from sheet entry
                        print(f"\n📅 Step 4: Creating calendar event from sheet entry...")
                        print(f"   📋 This will read live data from Google Sheets row {row_number}")
                        print(f"   📋 If you edited the sheet, it will use your updated data")
                        
                        response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                               json={"sheet_entry_id": sheet_entry_id},
                                               headers={'Content-Type': 'application/json'})
                        
                        print(f"Response status: {response.status_code}")
                        
                        if response.status_code == 200:
                            event_data = response.json()
                            event_details = event_data.get('event', {})
                            
                            print(f"✅ Calendar event created successfully!")
                            print(f"\n📊 Event Details (using live sheet data):")
                            print(f"   Event ID: {event_details.get('id')}")
                            print(f"   Name: {event_details.get('name')}")
                            print(f"   Email: {event_details.get('email')} ← FROM SHEETS")
                            print(f"   Phone: {event_details.get('phone')} ← FROM SHEETS")
                            print(f"   Date: {event_details.get('date')}")
                            print(f"   Time: {event_details.get('time')}")
                            print(f"   Location: {event_details.get('location')} ← FROM SHEETS")
                            print(f"   Event Link: {event_details.get('event_link')}")
                            
                            # Compare with original PII data
                            print(f"\n🔍 Data Source Comparison:")
                            original_email = pii_data.get('emails', [])[0] if pii_data.get('emails') else 'N/A'
                            original_phone = pii_data.get('phone_numbers', [])[0] if pii_data.get('phone_numbers') else 'N/A'
                            
                            print(f"   Original PII Email: {original_email}")
                            print(f"   Event Email:        {event_details.get('email')}")
                            print(f"   Original PII Phone: {original_phone}")
                            print(f"   Event Phone:        {event_details.get('phone')}")
                            
                            if event_details.get('email') != original_email:
                                print(f"   ✅ SUCCESS: Using updated email from sheets!")
                            else:
                                print(f"   📋 INFO: Email matches original (no changes in sheet)")
                            
                            return sheet_entry_id, row_number
                        else:
                            print(f"❌ Calendar event creation failed")
                            try:
                                error_data = response.json()
                                print(f"   Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   Raw response: {response.text}")
                    else:
                        print(f"❌ Save to sheets failed: {response.status_code}")
                        return None, None
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
                    return None, None
            else:
                print("❌ No transcripts found")
                return None, None
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None, None

def show_instructions(sheet_entry_id, row_number):
    """Show instructions for testing the live data feature"""
    print(f"\n📋 TESTING INSTRUCTIONS")
    print("=" * 60)
    
    if sheet_entry_id and row_number:
        print(f"🔧 To test the live data feature:")
        print(f"   1. Open your Google Sheets")
        print(f"   2. Go to row {row_number}")
        print(f"   3. Edit the email column (column B) to your test email")
        print(f"   4. Edit the contact column (column F) to your test phone")
        print(f"   5. Save the sheet")
        print(f"   6. Run this API call:")
        print(f"      POST /api/calendar/create_event_from_pii/")
        print(f"      Body: {{\"sheet_entry_id\": {sheet_entry_id}}}")
        print(f"   7. Check that the event uses your updated email/phone")
        print(f"\n💡 The API will now read live data from the sheet instead of the database!")
    else:
        print(f"❌ No sheet entry available for testing")

if __name__ == "__main__":
    print("🚀 LIVE GOOGLE SHEETS DATA TESTING")
    print("=" * 70)
    print("Testing that calendar events use live data from Google Sheets")
    print("instead of cached data from the internal database")
    print()
    
    # Test live sheet data usage
    sheet_entry_id, row_number = test_live_sheet_data_usage()
    
    # Show testing instructions
    show_instructions(sheet_entry_id, row_number)
    
    print("\n" + "=" * 70)
    print("✨ TESTING COMPLETED")
    print("=" * 70)
    print("\n📋 Summary:")
    print("• Calendar events now read live data from Google Sheets")
    print("• Edit the sheet and the API will use your updated data")
    print("• Email, phone, location, date, time all come from sheets")
    print("• Perfect for testing notifications with your real email")
    print(f"\n🎯 Use sheet_entry_id {sheet_entry_id} to test with your edited data!")
