# Diarization Process & PII Extraction Explanation

## 🎯 **What is Diarization?**

**Diarization** = **Speaker Separation** - It identifies "who spoke when" in an audio conversation.

## 📊 **Process Flow & Transcript Differences**

### **Complete Audio Processing Workflow:**

```
Audio File (MP3/WAV)
    ↓
1. Basic Processing (/process/)
    ↓
    Transcript 1: Simple text without speaker labels
    ↓
2. Diarization Processing (/process_with_diarization/)
    ↓
    Transcript 2: Text WITH speaker labels + timing
    ↓
3. Cleaning (/clean_transcript/)
    ↓
    Cleaned version of selected transcript
    ↓
4. PII Extraction (/extract_from_transcript/)
    ↓
    Structured PII data (names, emails, dates, etc.)
```

## 🔍 **Transcript Differences Explained**

### **Transcript 1 (Basic `/process/`):**
```
Hi, is this John? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is <PERSON> and my email <NAME_EMAIL>. Nice to meet you, <PERSON>. When would you like to schedule an appointment to see the house? How about 5th December? Okay, December works for me. Perfect. Thank you.
```

**Characteristics:**
- ✅ **Simple text conversion** from speech to text
- ❌ **No speaker identification** - can't tell who said what
- ❌ **No timing information** - don't know when things were said
- ⚡ **Faster processing** - basic transcription only
- 💰 **Lower cost** - simpler AI processing

### **Transcript 2 (Diarization `/process_with_diarization/`):**
```
[Agent 00:01-00:03]: Hi, is this John?
[Customer 00:05-00:06]: Yes, speaking.
[Agent 00:06-00:07]: How can I help you?
[Customer 00:07-00:09]: I'm calling regarding the house listing. I saw online.
[Agent 00:12-00:13]: Oh, great.
[Agent 00:13-00:15]: Could you share a few details with me?
[Customer 00:15-00:16]: Sure.
[Customer 00:16-00:21]: My name is Jane and my email <NAME_EMAIL>.
[Agent 00:23-00:24]: Nice to meet you, Jane.
[Agent 00:24-00:27]: When would you like to schedule an appointment to see the house?
[Customer 00:28-00:30]: How about 5th December?
[Agent 00:32-00:35]: Okay, December works for me.
[Customer 00:45-00:47]: Perfect.
[Agent 00:49-00:50]: Thank you.
```

**Characteristics:**
- ✅ **Speaker identification** - knows who said what (Agent vs Customer)
- ✅ **Timing information** - knows when each part was said
- ✅ **Better context** - can analyze agent performance vs customer needs
- ✅ **Structured data** - organized by speaker and time
- 🐌 **Slower processing** - more complex AI analysis
- 💰 **Higher cost** - advanced AI processing

## 🎯 **Why Use Diarization? Key Benefits**

### **1. Better PII Extraction:**
- **Without Diarization**: "My name is Jane" - could be anyone
- **With Diarization**: "[Customer]: My name is Jane" - clearly customer's info

### **2. Improved Analysis:**
- **Agent Performance**: How well did the agent handle the call?
- **Customer Satisfaction**: Was the customer happy with the service?
- **Call Quality**: Professional communication, resolution effectiveness

### **3. Better Business Intelligence:**
- **Agent Training**: Identify areas where agents need improvement
- **Customer Insights**: Understand customer pain points and needs
- **Process Optimization**: Improve call handling procedures

### **4. Compliance & Legal:**
- **Call Recording Compliance**: Know exactly who said what and when
- **Dispute Resolution**: Clear evidence of what was discussed
- **Quality Assurance**: Monitor agent compliance with scripts/procedures

## 🔧 **When to Use Each Process**

### **Use Basic Processing (`/process/`) When:**
- ✅ You just need text content
- ✅ Speed is more important than detail
- ✅ Cost optimization is priority
- ✅ Simple transcription is sufficient

### **Use Diarization (`/process_with_diarization/`) When:**
- ✅ You need to know who said what
- ✅ Analyzing agent performance
- ✅ Extracting customer-specific information
- ✅ Quality assurance and training
- ✅ Compliance requirements
- ✅ Better PII extraction accuracy

## 📊 **PII Extraction Differences**

### **From Basic Transcript:**
```json
{
    "names": ["Jane", "John", "Smith"],  // Mixed agent + customer names
    "emails": ["<EMAIL>"],
    "dates": ["5th December"],
    "context": "Unknown who provided what information"
}
```

### **From Diarized Transcript:**
```json
{
    "names": ["Jane Smith"],  // Only customer names (filtered by speaker)
    "emails": ["<EMAIL>"],  // Customer's email
    "dates": ["5th December"],  // Customer's preferred date
    "context": "Clear attribution - customer provided this information",
    "agent_info": "Filtered out agent's information",
    "customer_info": "Focused on customer's personal data"
}
```

## 🚀 **Recommended Workflow**

### **For Maximum Accuracy:**
1. **Upload Audio** → Audio file stored
2. **Diarization Processing** → `/process_with_diarization/` (creates Transcript with speaker labels)
3. **Clean Transcript** → `/clean_transcript/` (fixes speech-to-text errors)
4. **Extract PII** → `/extract_from_transcript/` (gets structured customer data)
5. **Analyze Call** → `/ira/analyze_transcript/` (generates quality report)

### **For Speed/Cost Optimization:**
1. **Upload Audio** → Audio file stored
2. **Basic Processing** → `/process/` (creates simple transcript)
3. **Clean Transcript** → `/clean_transcript/` (fixes errors)
4. **Extract PII** → `/extract_from_transcript/` (gets mixed data)

## 💡 **Best Practices**

### **For Customer Service Analysis:**
- ✅ **Always use diarization** for customer service calls
- ✅ **Clean transcripts** before PII extraction
- ✅ **Use latest PII ID** for validation and sheets export
- ✅ **Run IRA analysis** for quality assessment

### **For Simple Transcription:**
- ✅ **Use basic processing** for meetings, interviews, notes
- ✅ **Clean transcripts** for better readability
- ✅ **Extract PII** if personal information is present

## 🔍 **Technical Implementation**

### **Database Structure:**
```
AudioFile (1) → Transcripts (Many)
    ├── Transcript 1 (Basic processing)
    ├── Transcript 2 (Diarization processing)
    └── Transcript 3 (Cleaned version)

Transcript (1) → PIIData (1)
    └── Contains: names, emails, phones, addresses, dates

Transcript (1) → IRAReport (1)
    └── Contains: sentiment, quality score, summary
```

### **API Endpoints:**
- `POST /audio/{id}/process/` - Basic transcription
- `POST /audio/{id}/process_with_diarization/` - Speaker separation
- `POST /audio/{id}/clean_transcript/` - Clean latest transcript
- `POST /pii/extract_from_transcript/` - Extract PII data
- `POST /ira/analyze_transcript/` - Generate quality report

## 🎯 **Summary**

**Diarization** transforms a simple conversation transcript into a structured, speaker-identified, time-stamped analysis that enables:
- **Better PII extraction** (customer vs agent data separation)
- **Quality analysis** (agent performance evaluation)
- **Business intelligence** (customer insights and process optimization)
- **Compliance** (clear attribution and timing)

The choice between basic processing and diarization depends on your specific needs for accuracy, context, and analysis depth.
