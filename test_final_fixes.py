#!/usr/bin/env python3
"""
Final test to verify all PII extraction fixes
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_latest_pii_extraction():
    """Test PII extraction with the latest transcript"""
    print("🔍 TESTING LATEST PII EXTRACTION")
    print("=" * 50)
    
    # Get the latest transcript ID
    try:
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                latest_transcript = data['transcripts'][0]
                transcript_id = latest_transcript['id']
                print(f"📄 Latest transcript ID: {transcript_id}")
                print(f"   Preview: {latest_transcript['text'][:150]}...")
            else:
                print("❌ No transcripts found")
                return None
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None
    
    # Extract PII from the latest transcript
    print(f"\n🔍 Extracting PII from transcript {transcript_id}...")
    try:
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": transcript_id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            pii_data = data.get('pii_data', {})
            pii_id = pii_data.get('id')
            
            print(f"✅ PII extracted successfully")
            print(f"   PII ID: {pii_id}")
            print(f"   Transcript ID: {pii_data.get('transcript_id')}")
            
            # Display results
            names = pii_data.get('names', [])
            emails = pii_data.get('emails', [])
            dates = pii_data.get('appointment_dates', [])
            
            print(f"\n📊 Extraction Results:")
            print(f"   Names: {names}")
            print(f"   Emails: {emails}")
            print(f"   Dates: {dates}")
            
            # Quality assessment
            print(f"\n📈 Quality Assessment:")
            
            # Check names
            expected_names = ["John", "Jane"]
            unwanted_names = ["Hi", "Yes", "How", "Could", "Smith", "Jane and"]
            
            good_names = [name for name in names if name != "Not Found" and any(expected in name for expected in expected_names)]
            bad_names = [name for name in names if name != "Not Found" and any(unwanted in name for unwanted in unwanted_names)]
            
            if good_names:
                print(f"     ✅ Found expected names: {good_names}")
            else:
                print(f"     ❌ Missing expected names")
            
            if bad_names:
                print(f"     ❌ Found unwanted names: {bad_names}")
            else:
                print(f"     ✅ No unwanted names found")
            
            # Check emails
            if emails and emails != ["Not Found"]:
                valid_emails = [email for email in emails if "@" in email and "." in email]
                if valid_emails:
                    print(f"     ✅ Found valid emails: {valid_emails}")
                else:
                    print(f"     ❌ Invalid email formats: {emails}")
            else:
                print(f"     ❌ No emails found")
            
            # Check dates
            expected_dates = ["5th December", "7th December"]
            if dates and dates != ["Not Found"]:
                found_dates = [date for date in dates if any(expected in date for expected in expected_dates)]
                if found_dates:
                    print(f"     ✅ Found expected dates: {found_dates}")
                else:
                    print(f"     ⚠️  Found dates but not expected ones: {dates}")
            else:
                print(f"     ❌ No dates found")
            
            return pii_id
            
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_pii_endpoints(pii_id):
    """Test the PII endpoints with the correct ID"""
    print(f"\n🧪 TESTING PII ENDPOINTS WITH CORRECT ID {pii_id}")
    print("=" * 50)
    
    # Test validation
    print(f"🔍 Testing validation endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/pii/{pii_id}/validate/")
        if response.status_code == 200:
            data = response.json()
            validation_results = data.get('validation_results', {})
            
            names = validation_results.get('names', [])
            emails = validation_results.get('emails', [])
            
            print(f"✅ Validation successful")
            print(f"   Validated names: {[n['value'] for n in names]}")
            print(f"   Validated emails: {[e['value'] for e in emails]}")
            
            # Check if we're getting clean results
            name_values = [n['value'] for n in names]
            unwanted = ['Hi', 'Yes', 'How', 'Could', 'Oh']
            clean_names = [name for name in name_values if not any(unwanted_word in name for unwanted_word in unwanted)]
            
            if len(clean_names) == len(name_values):
                print(f"     ✅ All names are clean")
            else:
                print(f"     ❌ Some unwanted names found")
            
        else:
            print(f"❌ Validation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Validation error: {str(e)}")
    
    # Test save to sheets
    print(f"\n📊 Testing save to sheets...")
    try:
        response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Saved to sheets successfully")
            print(f"   Sheet entry ID: {data.get('sheet_entry_id')}")
            print(f"   Row number: {data.get('row_number')}")
        else:
            print(f"❌ Save to sheets failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Save to sheets error: {str(e)}")

def test_ira_analysis():
    """Test IRA analysis endpoint"""
    print(f"\n🧠 TESTING IRA ANALYSIS")
    print("=" * 50)
    
    try:
        # Get latest transcript
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                print(f"🧠 Testing IRA analysis for transcript {transcript_id}...")
                response = requests.post(f"{BASE_URL}/ira/analyze_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ IRA analysis successful")
                    print(f"   Report ID: {data.get('report_id')}")
                else:
                    print(f"❌ IRA analysis failed: {response.status_code}")
                    print(f"   Response: {response.text}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
    except Exception as e:
        print(f"❌ IRA analysis error: {str(e)}")

def main():
    print("🚀 FINAL PII EXTRACTION FIXES TEST")
    print("=" * 60)
    print("Testing all fixes and improvements")
    print("Make sure Django server is running on http://127.0.0.1:8000")
    print()
    
    # Test latest PII extraction
    pii_id = test_latest_pii_extraction()
    
    # Test endpoints with correct PII ID
    if pii_id:
        test_pii_endpoints(pii_id)
    else:
        print("\n❌ No PII ID available for endpoint testing")
    
    # Test IRA analysis
    test_ira_analysis()
    
    print("\n" + "=" * 60)
    print("✨ FINAL TESTING COMPLETED")
    print("=" * 60)
    print("\n📋 Key Points:")
    print("• Always use the LATEST PII ID for your API calls")
    print("• PII ID 5 should have clean names like ['Jane', 'John']")
    print("• Avoid using old PII IDs (1, 2, 3, 4) which have bad data")
    print("• Check Google Sheets to see the clean data being saved")
    print("• Email extraction should work for '<EMAIL>' format")
    print("\n🎯 Your PII extraction system is now significantly improved!")

if __name__ == "__main__":
    main()
