#!/usr/bin/env python3
"""
Debug script to understand the Groq package structure
"""

import os

def debug_groq_package():
    """Debug the Groq package to understand the correct initialization"""
    print("🔍 DEBUGGING GROQ PACKAGE")
    print("=" * 50)
    
    try:
        import groq
        print(f"✅ Groq package imported successfully")
        print(f"   Version: {getattr(groq, '__version__', 'Unknown')}")
        print(f"   Available attributes: {[attr for attr in dir(groq) if not attr.startswith('_')]}")
        
        # Check if Groq class exists
        if hasattr(groq, 'Groq'):
            print(f"✅ Groq class found")
            
            # Check Groq class constructor
            import inspect
            groq_class = groq.Groq
            signature = inspect.signature(groq_class.__init__)
            print(f"   Groq.__init__ parameters: {list(signature.parameters.keys())}")
            
            # Try to initialize with minimal parameters
            try:
                api_key = os.getenv('GROQ_API_KEY')
                if api_key:
                    client = groq.Groq(api_key=api_key)
                    print(f"✅ Groq client initialized successfully with api_key only")
                else:
                    print(f"❌ No GROQ_API_KEY found")
            except Exception as e:
                print(f"❌ Groq initialization failed: {str(e)}")
        
        # Check if Client class exists (older versions)
        if hasattr(groq, 'Client'):
            print(f"✅ Client class found (older version)")
            
            # Check Client class constructor
            client_class = groq.Client
            signature = inspect.signature(client_class.__init__)
            print(f"   Client.__init__ parameters: {list(signature.parameters.keys())}")
        
        # Check for other possible classes
        classes = [attr for attr in dir(groq) if attr[0].isupper() and hasattr(getattr(groq, attr), '__init__')]
        print(f"   Available classes: {classes}")
        
    except ImportError as e:
        print(f"❌ Could not import groq: {str(e)}")
    except Exception as e:
        print(f"❌ Error debugging groq: {str(e)}")

def test_simple_groq_init():
    """Test the simplest possible Groq initialization"""
    print("\n🧪 TESTING SIMPLE GROQ INITIALIZATION")
    print("=" * 50)
    
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        print("❌ No GROQ_API_KEY found")
        return
    
    try:
        # Method 1: Just api_key
        print("Method 1: Groq(api_key=api_key)")
        import groq
        client = groq.Groq(api_key=api_key)
        print("✅ Success!")
        return client
    except Exception as e:
        print(f"❌ Failed: {str(e)}")
    
    try:
        # Method 2: Using keyword arguments explicitly
        print("Method 2: Groq(**{'api_key': api_key})")
        import groq
        client = groq.Groq(**{'api_key': api_key})
        print("✅ Success!")
        return client
    except Exception as e:
        print(f"❌ Failed: {str(e)}")
    
    try:
        # Method 3: Check if there's a different way to initialize
        print("Method 3: Checking alternative initialization")
        import groq
        
        # Try to find the correct way by checking the source
        if hasattr(groq, 'Groq'):
            groq_class = groq.Groq
            # Get the actual constructor parameters
            import inspect
            sig = inspect.signature(groq_class.__init__)
            params = list(sig.parameters.keys())
            print(f"   Available parameters: {params}")
            
            # Try with only the required parameters
            if 'api_key' in params:
                client = groq_class(api_key=api_key)
                print("✅ Success with api_key parameter!")
                return client
            
    except Exception as e:
        print(f"❌ Failed: {str(e)}")
    
    print("❌ All initialization methods failed")
    return None

def test_groq_api_call(client):
    """Test a simple API call with the client"""
    print("\n📞 TESTING GROQ API CALL")
    print("=" * 50)
    
    if not client:
        print("❌ No client available for testing")
        return
    
    try:
        response = client.chat.completions.create(
            model="mixtral-8x7b-32768",
            messages=[{"role": "user", "content": "Say hello in one word"}],
            max_tokens=10
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ API call successful: {result}")
        
    except Exception as e:
        print(f"❌ API call failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 GROQ PACKAGE DEBUG")
    print("=" * 60)
    
    # Debug the package structure
    debug_groq_package()
    
    # Test simple initialization
    client = test_simple_groq_init()
    
    # Test API call
    test_groq_api_call(client)
    
    print("\n" + "=" * 60)
    print("✨ DEBUG COMPLETED")
    print("=" * 60)
