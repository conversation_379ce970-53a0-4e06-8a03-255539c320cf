from django.contrib import admin
from .models import AudioFile, Transcript, Speaker, Dialogue

@admin.register(AudioFile)
class AudioFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'uploaded_at', 'processed')
    list_filter = ('processed', 'uploaded_at')
    search_fields = ('id',)

@admin.register(Transcript)
class TranscriptAdmin(admin.ModelAdmin):
    list_display = ('id', 'audio', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('text',)

@admin.register(Speaker)
class SpeakerAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'transcript')
    list_filter = ('name',)
    search_fields = ('name',)

@admin.register(Dialogue)
class DialogueAdmin(admin.ModelAdmin):
    list_display = ('id', 'speaker', 'start_time', 'end_time')
    list_filter = ('speaker',)
    search_fields = ('text',)

