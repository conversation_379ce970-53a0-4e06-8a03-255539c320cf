#!/usr/bin/env python3
"""
Test real email notifications with the updated Gmail SMTP service
"""

import requests
import json
import os

BASE_URL = "http://127.0.0.1:8000/api"

def check_email_configuration():
    """Check if email credentials are configured"""
    print("📧 CHECKING EMAIL CONFIGURATION")
    print("=" * 50)
    
    sender_email = os.getenv('SENDER_EMAIL', 'not set')
    sender_password = os.getenv('SENDER_PASSWORD', 'not set')
    
    print(f"📧 Gmail SMTP Configuration:")
    print(f"   SENDER_EMAIL: {sender_email}")
    print(f"   SENDER_PASSWORD: {'*' * len(sender_password) if sender_password != 'not set' else 'not set'}")
    
    if sender_email == 'not set' or sender_password == 'not set':
        print(f"\n⚠️  EMAIL CREDENTIALS NOT CONFIGURED")
        print(f"   Emails will be MOCK/FAKE (printed to console only)")
        print(f"   To enable real emails:")
        print(f"   1. Enable 2-Factor Authentication on Gmail")
        print(f"   2. Generate App Password: Gmail → Security → App passwords")
        print(f"   3. Set environment variables:")
        print(f"      set SENDER_EMAIL=<EMAIL>")
        print(f"      set SENDER_PASSWORD=your-16-char-app-password")
        print(f"   4. Restart Django server")
        return False
    else:
        print(f"\n✅ EMAIL CREDENTIALS CONFIGURED")
        print(f"   Emails will be sent via Gmail SMTP")
        return True

def test_complete_workflow():
    """Test the complete workflow: PII → Sheets → Calendar → Notifications"""
    print(f"\n🔄 TESTING COMPLETE WORKFLOW")
    print("=" * 50)
    
    try:
        # Step 1: Extract PII
        print("📄 Step 1: Extracting PII from latest transcript...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    print(f"✅ PII extracted - ID: {pii_id}")
                    print(f"   Names: {pii_data.get('names', [])}")
                    print(f"   Emails: {pii_data.get('emails', [])}")
                    print(f"   Phones: {pii_data.get('phone_numbers', [])}")
                    
                    # Step 2: Save to Google Sheets
                    print(f"\n📊 Step 2: Saving to Google Sheets...")
                    response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
                    
                    if response.status_code == 200:
                        sheet_data = response.json()
                        sheet_entry_id = sheet_data.get('sheet_entry_id')
                        row_number = sheet_data.get('row_number')
                        
                        print(f"✅ Saved to sheets - Entry ID: {sheet_entry_id}, Row: {row_number}")
                        
                        # Step 3: Create calendar event from sheet entry
                        print(f"\n📅 Step 3: Creating calendar event from sheet entry...")
                        response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                               json={"sheet_entry_id": sheet_entry_id},
                                               headers={'Content-Type': 'application/json'})
                        
                        if response.status_code == 200:
                            event_data = response.json()
                            event_details = event_data.get('event', {})
                            event_id = event_details.get('id')
                            
                            print(f"✅ Calendar event created - ID: {event_id}")
                            print(f"   Name: {event_details.get('name')}")
                            print(f"   Email: {event_details.get('email')}")
                            print(f"   Phone: {event_details.get('phone')}")
                            print(f"   Date: {event_details.get('date')}")
                            print(f"   Time: {event_details.get('time')}")
                            
                            # Step 4: Send notifications
                            print(f"\n📬 Step 4: Sending notifications...")
                            print(f"   📧 Email will be sent to: {event_details.get('email')}")
                            print(f"   📱 SMS will be sent to: {event_details.get('phone')}")
                            
                            response = requests.post(f"{BASE_URL}/calendar/{event_id}/send_notifications/")
                            
                            if response.status_code == 200:
                                notification_data = response.json()
                                print(f"✅ Notifications sent successfully!")
                                print(f"   Response: {json.dumps(notification_data, indent=2)}")
                                
                                # Check if real email was sent
                                sender_email = os.getenv('SENDER_EMAIL')
                                if sender_email:
                                    print(f"\n📧 REAL EMAIL SENT!")
                                    print(f"   From: {sender_email}")
                                    print(f"   To: {event_details.get('email')}")
                                    print(f"   Subject: Appointment Confirmation")
                                    print(f"   Check your email inbox!")
                                else:
                                    print(f"\n📧 MOCK EMAIL (no credentials configured)")
                                    print(f"   Check Django console for email content")
                                
                                return event_id
                            else:
                                print(f"❌ Notification sending failed: {response.status_code}")
                                try:
                                    error_data = response.json()
                                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                                except:
                                    print(f"   Raw response: {response.text}")
                        else:
                            print(f"❌ Calendar event creation failed: {response.status_code}")
                    else:
                        print(f"❌ Save to sheets failed: {response.status_code}")
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def show_setup_instructions():
    """Show setup instructions for real email notifications"""
    print(f"\n📋 SETUP INSTRUCTIONS FOR REAL EMAIL NOTIFICATIONS")
    print("=" * 60)
    
    print(f"🔧 To receive REAL email notifications:")
    print(f"")
    print(f"1. 📧 Set up Gmail App Password:")
    print(f"   • Go to your Gmail account")
    print(f"   • Settings → Security → 2-Step Verification")
    print(f"   • App passwords → Generate password for 'Mail'")
    print(f"   • Copy the 16-character password")
    print(f"")
    print(f"2. 🔧 Set Environment Variables (Windows):")
    print(f"   set SENDER_EMAIL=<EMAIL>")
    print(f"   set SENDER_PASSWORD=your-16-char-app-password")
    print(f"")
    print(f"3. 🔄 Restart Django Server:")
    print(f"   python manage.py runserver")
    print(f"")
    print(f"4. 📊 Edit Google Sheets:")
    print(f"   • Open your Google Sheets")
    print(f"   • Find the row with your data")
    print(f"   • Change the email column to your real email")
    print(f"   • Save the sheet")
    print(f"")
    print(f"5. 🧪 Test Notifications:")
    print(f"   • Run this script again")
    print(f"   • Or use the API directly:")
    print(f"     POST /api/calendar/create_event_from_pii/")
    print(f"     {{\"sheet_entry_id\": 12}}")
    print(f"     POST /api/calendar/{{event_id}}/send_notifications/")
    print(f"")
    print(f"6. 📧 Check Your Email:")
    print(f"   • Look for 'Appointment Confirmation' email")
    print(f"   • Check spam folder if not in inbox")

if __name__ == "__main__":
    print("🚀 REAL EMAIL NOTIFICATION TESTING")
    print("=" * 70)
    print("Testing the updated Gmail SMTP email service")
    print()
    
    # Check email configuration
    email_configured = check_email_configuration()
    
    # Test complete workflow
    event_id = test_complete_workflow()
    
    # Show setup instructions
    show_setup_instructions()
    
    print("\n" + "=" * 70)
    print("✨ TESTING COMPLETED")
    print("=" * 70)
    print("\n📋 Summary:")
    if email_configured:
        print("• ✅ Email credentials are configured")
        print("• ✅ Real emails will be sent via Gmail SMTP")
        print("• 📧 Check your email inbox for notifications")
    else:
        print("• ⚠️  Email credentials not configured")
        print("• 📧 Emails are currently MOCK/FAKE (console only)")
        print("• 🔧 Follow setup instructions above to enable real emails")
    
    print("• 📱 SMS is still MOCK (set up Twilio for real SMS)")
    print(f"• 🎯 Use event ID {event_id} for testing notifications")
    
    print("\n💡 Quick Test:")
    print("1. Set SENDER_EMAIL and SENDER_PASSWORD environment variables")
    print("2. Edit Google Sheet with your real email")
    print("3. Run: POST /api/calendar/create_event_from_pii/ with sheet_entry_id")
    print("4. Run: POST /api/calendar/{event_id}/send_notifications/")
    print("5. Check your email inbox!")
