#!/usr/bin/env python3
"""
Script to fix PII data issues and test the correct endpoints
"""

import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

from pii_extraction.models import PIIData
from audio_processing.models import Transcript

BASE_URL = "http://127.0.0.1:8000/api"

def check_pii_data():
    """Check current PII data in database"""
    print("🔍 CHECKING CURRENT PII DATA")
    print("=" * 50)
    
    try:
        pii_records = PIIData.objects.all().order_by('-created_at')
        
        print(f"Found {len(pii_records)} PII records:")
        for pii in pii_records:
            print(f"\n  PII ID: {pii.id}")
            print(f"  Transcript ID: {pii.transcript.id}")
            print(f"  Names: {pii.names}")
            print(f"  Emails: {pii.emails}")
            print(f"  Dates: {pii.appointment_dates}")
            print(f"  Created: {pii.created_at}")
            print(f"  Updated: {pii.updated_at}")
            print("-" * 30)
        
        # Find the latest PII record
        if pii_records:
            latest_pii = pii_records[0]
            print(f"\n✅ Latest PII record: ID {latest_pii.id} (Transcript {latest_pii.transcript.id})")
            return latest_pii.id
        else:
            print("❌ No PII records found")
            return None
            
    except Exception as e:
        print(f"❌ Error checking PII data: {str(e)}")
        return None

def regenerate_pii_for_transcript():
    """Regenerate PII for the latest transcript"""
    print("\n🔄 REGENERATING PII FOR LATEST TRANSCRIPT")
    print("=" * 50)
    
    try:
        # Get the latest transcript
        transcript = Transcript.objects.order_by('-created_at').first()
        if not transcript:
            print("❌ No transcripts found")
            return None
        
        print(f"📄 Using transcript ID: {transcript.id}")
        print(f"   Text preview: {transcript.text[:150]}...")
        
        # Call the API to extract PII
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": transcript.id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            pii_data = data.get('pii_data', {})
            
            print(f"✅ PII extracted successfully")
            print(f"   New PII ID: {pii_data.get('id')}")
            print(f"   Names: {pii_data.get('names', [])}")
            print(f"   Emails: {pii_data.get('emails', [])}")
            print(f"   Dates: {pii_data.get('appointment_dates', [])}")
            
            return pii_data.get('id')
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error regenerating PII: {str(e)}")
        return None

def test_pii_endpoints(pii_id):
    """Test the PII endpoints with the correct ID"""
    print(f"\n🧪 TESTING PII ENDPOINTS WITH ID {pii_id}")
    print("=" * 50)
    
    # Test validation endpoint
    print(f"🔍 Testing validation endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/pii/{pii_id}/validate/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Validation successful")
            
            validation_results = data.get('validation_results', {})
            names = validation_results.get('names', [])
            print(f"   Validated names: {[n['value'] for n in names]}")
            
            emails = validation_results.get('emails', [])
            print(f"   Validated emails: {[e['value'] for e in emails]}")
            
        else:
            print(f"❌ Validation failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Validation error: {str(e)}")
    
    # Test save to sheets endpoint
    print(f"\n📊 Testing save to sheets endpoint...")
    try:
        response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Saved to sheets successfully")
            print(f"   Sheet entry ID: {data.get('sheet_entry_id')}")
            print(f"   Row number: {data.get('row_number')}")
        else:
            print(f"❌ Save to sheets failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Save to sheets error: {str(e)}")

def fix_groq_client_issue():
    """Check and fix the Groq client configuration issue"""
    print(f"\n🔧 CHECKING GROQ CLIENT CONFIGURATION")
    print("=" * 50)
    
    try:
        from pii_extraction.groq_services import GroqPIIExtractor
        
        # Try to initialize Groq client
        try:
            groq_extractor = GroqPIIExtractor()
            print("✅ Groq client initialized successfully")
        except Exception as e:
            print(f"❌ Groq client initialization failed: {str(e)}")
            
            # Check if it's the proxies issue
            if "proxies" in str(e):
                print("🔧 Detected 'proxies' parameter issue")
                print("   This is likely due to an outdated Groq client version")
                print("   Recommendation: Update groq package with 'pip install --upgrade groq'")
            
            return False
        
        # Test IRA analysis endpoint
        print(f"\n🧪 Testing IRA analysis endpoint...")
        try:
            transcript = Transcript.objects.order_by('-created_at').first()
            if transcript:
                response = requests.post(f"{BASE_URL}/ira/analyze_transcript/", 
                                       json={"transcript_id": transcript.id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ IRA analysis successful")
                    print(f"   Report ID: {data.get('report_id')}")
                else:
                    print(f"❌ IRA analysis failed: {response.status_code}")
                    print(f"   Response: {response.text}")
            else:
                print("❌ No transcript found for IRA analysis")
                
        except Exception as e:
            print(f"❌ IRA analysis error: {str(e)}")
            
    except ImportError as e:
        print(f"❌ Could not import GroqPIIExtractor: {str(e)}")
        return False
    
    return True

def main():
    print("🚀 PII DATA FIX AND TEST SCRIPT")
    print("=" * 60)
    print("This script will:")
    print("1. Check current PII data in database")
    print("2. Regenerate PII for latest transcript")
    print("3. Test PII endpoints with correct ID")
    print("4. Check Groq client configuration")
    print()
    
    # Step 1: Check current PII data
    current_pii_id = check_pii_data()
    
    # Step 2: Regenerate PII for latest transcript
    new_pii_id = regenerate_pii_for_transcript()
    
    # Step 3: Test endpoints with the correct PII ID
    if new_pii_id:
        test_pii_endpoints(new_pii_id)
    elif current_pii_id:
        print(f"\n⚠️  Using existing PII ID {current_pii_id} for testing")
        test_pii_endpoints(current_pii_id)
    else:
        print("\n❌ No PII data available for testing")
    
    # Step 4: Check Groq configuration
    fix_groq_client_issue()
    
    print("\n" + "=" * 60)
    print("✨ FIX AND TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• Use the latest PII ID for your API calls")
    print("• Check that names are clean (no 'Hi', 'Yes', 'How')")
    print("• Verify emails are properly formatted")
    print("• Ensure dates are extracted correctly")
    print("• Update Groq package if needed: pip install --upgrade groq")
    print(f"\n🎯 Use PII ID {new_pii_id or current_pii_id} for your API calls!")

if __name__ == "__main__":
    main()
