from django.db import models
from audio_processing.models import Transcript

class PIIData(models.Model):
    transcript = models.OneToOneField(Transcript, on_delete=models.CASCADE, related_name='pii_data')
    names = models.J<PERSON><PERSON><PERSON>(default=list)
    phone_numbers = models.J<PERSON><PERSON>ield(default=list)
    emails = models.JSONField(default=list)
    addresses = models.JSONField(default=list)
    appointment_dates = models.JSONField(default=list)
    appointment_times = models.JSONField(default=list)
    ssn = models.JSONField(default=list)  # Added SSN field
    credit_cards = models.<PERSON><PERSON><PERSON><PERSON>(default=list)  # Added credit card field
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confidence_scores = models.JSONField(default=dict)  # Store confidence scores
    
    def __str__(self):
        return f"PII Data for Transcript {self.transcript.id}"

class GoogleSheetsEntry(models.Model):
    pii_data = models.ForeignKey(PIIData, on_delete=models.CASCADE, related_name='sheet_entries')
    sheet_id = models.CharField(max_length=255)
    row_number = models.IntegerField()
    processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Sheet Entry {self.row_number} for PII {self.pii_data.id}"

class PIIValidation(models.Model):
    """Store validation results for PII data"""
    pii_data = models.OneToOneField(PIIData, on_delete=models.CASCADE, related_name='validation')
    validation_results = models.JSONField(default=dict)
    overall_confidence = models.FloatField(default=0.0)
    validated_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Validation for PII {self.pii_data.id} - Confidence: {self.overall_confidence}"

