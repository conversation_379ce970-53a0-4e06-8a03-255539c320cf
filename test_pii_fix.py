#!/usr/bin/env python3
"""
Test the PII extraction fix for the organizations field error
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_pii_extraction_fix():
    """Test PII extraction after fixing the organizations field error"""
    print("🔍 TESTING PII EXTRACTION FIX")
    print("=" * 50)
    
    try:
        # Get the latest transcript
        print("📄 Getting latest transcript...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                print(f"✅ Found transcript ID: {transcript_id}")
                print(f"   Preview: {data['transcripts'][0]['text'][:150]}...")
                
                # Test PII extraction
                print(f"\n🔍 Extracting PII from transcript {transcript_id}...")
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    pii_data = data.get('pii_data', {})
                    
                    print(f"✅ PII extraction successful!")
                    print(f"   PII ID: {pii_data.get('id')}")
                    print(f"   Transcript ID: {pii_data.get('transcript_id')}")
                    
                    # Display results
                    print(f"\n📊 Extraction Results:")
                    for field in ['names', 'emails', 'phone_numbers', 'addresses', 'appointment_dates', 'appointment_times']:
                        values = pii_data.get(field, [])
                        clean_values = [v for v in values if v != "Not Found"]
                        if clean_values:
                            print(f"   {field.title()}: {clean_values}")
                        else:
                            print(f"   {field.title()}: None found")
                    
                    # Quality assessment
                    print(f"\n📈 Quality Assessment:")
                    
                    names = pii_data.get('names', [])
                    expected_names = ["John", "Jane"]
                    unwanted_names = ["Hi", "Yes", "How", "Could", "Smith", "Jane and"]
                    
                    good_names = [name for name in names if name != "Not Found" and any(expected in name for expected in expected_names)]
                    bad_names = [name for name in names if name != "Not Found" and any(unwanted in name for unwanted in unwanted_names)]
                    
                    if good_names:
                        print(f"     ✅ Found expected names: {good_names}")
                    else:
                        print(f"     ❌ Missing expected names")
                    
                    if bad_names:
                        print(f"     ❌ Found unwanted names: {bad_names}")
                    else:
                        print(f"     ✅ No unwanted names found")
                    
                    # Check emails
                    emails = pii_data.get('emails', [])
                    if emails and emails != ["Not Found"]:
                        valid_emails = [email for email in emails if "@" in email and "." in email]
                        if valid_emails:
                            print(f"     ✅ Found valid emails: {valid_emails}")
                        else:
                            print(f"     ❌ Invalid email formats: {emails}")
                    else:
                        print(f"     ❌ No emails found")
                    
                    # Check dates
                    dates = pii_data.get('appointment_dates', [])
                    expected_dates = ["5th December", "7th December"]
                    if dates and dates != ["Not Found"]:
                        found_dates = [date for date in dates if any(expected in date for expected in expected_dates)]
                        if found_dates:
                            print(f"     ✅ Found expected dates: {found_dates}")
                        else:
                            print(f"     ⚠️  Found dates but not expected ones: {dates}")
                    else:
                        print(f"     ❌ No dates found")
                    
                    return pii_data.get('id')
                    
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   Error: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   Raw response: {response.text}")
                    return None
            else:
                print("❌ No transcripts found")
                return None
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_pii_endpoints(pii_id):
    """Test PII endpoints with the new PII ID"""
    print(f"\n🧪 TESTING PII ENDPOINTS WITH ID {pii_id}")
    print("=" * 50)
    
    if not pii_id:
        print("❌ No PII ID available for testing")
        return
    
    # Test validation
    print(f"🔍 Testing validation endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/pii/{pii_id}/validate/")
        if response.status_code == 200:
            data = response.json()
            validation_results = data.get('validation_results', {})
            
            names = validation_results.get('names', [])
            emails = validation_results.get('emails', [])
            
            print(f"✅ Validation successful")
            print(f"   Validated names: {[n['value'] for n in names]}")
            print(f"   Validated emails: {[e['value'] for e in emails]}")
            
        else:
            print(f"❌ Validation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Validation error: {str(e)}")
    
    # Test save to sheets
    print(f"\n📊 Testing save to sheets...")
    try:
        response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Saved to sheets successfully")
            print(f"   Sheet entry ID: {data.get('sheet_entry_id')}")
            print(f"   Row number: {data.get('row_number')}")
        else:
            print(f"❌ Save to sheets failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Save to sheets error: {str(e)}")

if __name__ == "__main__":
    print("🚀 PII EXTRACTION FIX TEST")
    print("=" * 60)
    print("Testing the fix for 'organizations' field error")
    print()
    
    # Test PII extraction
    pii_id = test_pii_extraction_fix()
    
    # Test endpoints with new PII ID
    test_pii_endpoints(pii_id)
    
    print("\n" + "=" * 60)
    print("✨ PII FIX TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• PII extraction should now work without 'organizations' field error")
    print("• Names should be clean (Jane, John) without unwanted words")
    print("• Emails should be properly formatted")
    print("• Dates should be extracted correctly")
    print("• Use the latest PII ID for validation and sheets export")
    print(f"\n🎯 Use PII ID {pii_id} for your API calls!")
