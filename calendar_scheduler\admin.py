from django.contrib import admin
from .models import CalendarEvent, EmailNotification, SMSNotification

@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'email', 'date', 'time', 'created_at')
    list_filter = ('date', 'created_at')
    search_fields = ('name', 'email', 'phone')

@admin.register(EmailNotification)
class EmailNotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'calendar_event', 'sent_at', 'status')
    list_filter = ('sent_at', 'status')
    search_fields = ('calendar_event__name',)

@admin.register(SMSNotification)
class SMSNotificationAdmin(admin.ModelAdmin):
    list_display = ('id', 'calendar_event', 'sent_at', 'status')
    list_filter = ('sent_at', 'status')
    search_fields = ('calendar_event__name',)

