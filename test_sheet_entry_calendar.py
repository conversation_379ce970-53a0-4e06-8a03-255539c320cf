#!/usr/bin/env python3
"""
Test the updated calendar API that accepts sheet entry IDs
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_calendar_with_sheet_entry():
    """Test creating calendar event from sheet entry ID"""
    print("📊 TESTING CALENDAR EVENT FROM SHEET ENTRY")
    print("=" * 60)
    
    try:
        # First, get latest PII data and save to sheets to get a sheet entry ID
        print("📄 Step 1: Getting latest transcript and extracting PII...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    print(f"✅ PII extracted - ID: {pii_id}")
                    print(f"   Names: {pii_data.get('names', [])}")
                    print(f"   Emails: {pii_data.get('emails', [])}")
                    print(f"   Dates: {pii_data.get('appointment_dates', [])}")
                    print(f"   Times: {pii_data.get('appointment_times', [])}")
                    
                    # Save to Google Sheets to get sheet entry ID
                    print(f"\n📊 Step 2: Saving to Google Sheets...")
                    response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
                    
                    if response.status_code == 200:
                        sheet_data = response.json()
                        sheet_entry_id = sheet_data.get('sheet_entry_id')
                        
                        print(f"✅ Saved to sheets - Entry ID: {sheet_entry_id}")
                        print(f"   Row number: {sheet_data.get('row_number')}")
                        
                        # Now test calendar creation with sheet entry ID
                        print(f"\n📅 Step 3: Creating calendar event from sheet entry...")
                        
                        # Test the updated API with sheet_entry_id
                        response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                               json={"sheet_entry_id": sheet_entry_id},
                                               headers={'Content-Type': 'application/json'})
                        
                        print(f"Response status: {response.status_code}")
                        
                        if response.status_code == 200:
                            event_data = response.json()
                            print(f"✅ Calendar event created successfully!")
                            print(f"   Event details: {json.dumps(event_data, indent=2)}")
                            
                            return sheet_entry_id
                        else:
                            print(f"❌ Calendar event creation failed")
                            try:
                                error_data = response.json()
                                print(f"   Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   Raw response: {response.text}")
                    else:
                        print(f"❌ Save to sheets failed: {response.status_code}")
                        return None
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
                    return None
            else:
                print("❌ No transcripts found")
                return None
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_both_api_methods():
    """Test both pii_data_id and sheet_entry_id methods"""
    print(f"\n🔄 TESTING BOTH API METHODS")
    print("=" * 60)
    
    try:
        # Get latest PII data
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    # Test 1: Using pii_data_id (original method)
                    print(f"🧪 Test 1: Using pii_data_id = {pii_id}")
                    response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                           json={"pii_data_id": pii_id},
                                           headers={'Content-Type': 'application/json'})
                    
                    if response.status_code == 200:
                        print(f"✅ Method 1 (pii_data_id) - SUCCESS")
                    else:
                        print(f"❌ Method 1 (pii_data_id) - FAILED: {response.status_code}")
                        try:
                            error_data = response.json()
                            print(f"   Error: {error_data.get('error', 'Unknown error')}")
                        except:
                            print(f"   Raw response: {response.text}")
                    
                    # Save to sheets first for method 2
                    response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
                    if response.status_code == 200:
                        sheet_data = response.json()
                        sheet_entry_id = sheet_data.get('sheet_entry_id')
                        
                        # Test 2: Using sheet_entry_id (new method)
                        print(f"\n🧪 Test 2: Using sheet_entry_id = {sheet_entry_id}")
                        response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                               json={"sheet_entry_id": sheet_entry_id},
                                               headers={'Content-Type': 'application/json'})
                        
                        if response.status_code == 200:
                            print(f"✅ Method 2 (sheet_entry_id) - SUCCESS")
                        else:
                            print(f"❌ Method 2 (sheet_entry_id) - FAILED: {response.status_code}")
                            try:
                                error_data = response.json()
                                print(f"   Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   Raw response: {response.text}")
                    
                    # Test 3: Error case - no ID provided
                    print(f"\n🧪 Test 3: No ID provided (should fail)")
                    response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                           json={},
                                           headers={'Content-Type': 'application/json'})
                    
                    if response.status_code == 400:
                        print(f"✅ Method 3 (no ID) - CORRECTLY FAILED with 400")
                        try:
                            error_data = response.json()
                            print(f"   Expected error: {error_data.get('error', 'Unknown error')}")
                        except:
                            pass
                    else:
                        print(f"❌ Method 3 (no ID) - Should have failed with 400, got {response.status_code}")
                        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 UPDATED CALENDAR API TESTING")
    print("=" * 70)
    print("Testing the updated create_event_from_pii API that accepts:")
    print("• pii_data_id (original method)")
    print("• sheet_entry_id (new method)")
    print("• Requires name and email at minimum")
    print()
    
    # Test calendar creation from sheet entry
    sheet_entry_id = test_calendar_with_sheet_entry()
    
    # Test both API methods
    test_both_api_methods()
    
    print("\n" + "=" * 70)
    print("✨ TESTING COMPLETED")
    print("=" * 70)
    print("\n📋 API Usage Summary:")
    print("• POST /api/calendar/create_event_from_pii/")
    print("  Body: {\"pii_data_id\": 5}  OR  {\"sheet_entry_id\": 10}")
    print("• Requires: name and email in the data")
    print("• Optional: dates, times (will use defaults if missing)")
    print("• Returns: Calendar event details")
    print(f"\n🎯 Example: Use sheet_entry_id {sheet_entry_id} for your API calls!")
