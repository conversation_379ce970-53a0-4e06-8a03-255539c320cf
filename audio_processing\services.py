import whisper
import torch
import os
import tempfile
from datetime import datetime
from django.conf import settings
from .models import AudioFile, Transcript, Speaker, Dialogue

class AudioProcessor:
    def __init__(self, model_name="base"):
        """Initialize the audio processor with Whisper model"""
        self.model = whisper.load_model(model_name)
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        
    def transcribe_audio(self, audio_file_path):
        """
        Transcribe audio file using Whisper
        Returns transcript text and word-level timestamps
        """
        try:
            # Load and transcribe audio
            result = self.model.transcribe(
                audio_file_path,
                word_timestamps=True,
                verbose=True
            )
            
            return {
                'text': result['text'],
                'segments': result['segments'],
                'language': result['language']
            }
        except Exception as e:
            raise Exception(f"Error transcribing audio: {str(e)}")
    
    def process_audio_file(self, audio_file_instance):
        """
        Process an AudioFile instance:
        1. Transcribe audio
        2. Create transcript record
        3. Assign speakers (basic implementation)
        4. Create dialogue records
        """
        try:
            # Get the file path
            audio_path = audio_file_instance.file.path
            
            # Transcribe audio
            transcription_result = self.transcribe_audio(audio_path)
            
            # Create transcript record
            transcript = Transcript.objects.create(
                audio=audio_file_instance,
                text=transcription_result['text']
            )
            
            # Create speakers (basic implementation - will be enhanced with pyannote)
            agent_speaker = Speaker.objects.create(
                transcript=transcript,
                name="agent"
            )
            customer_speaker = Speaker.objects.create(
                transcript=transcript,
                name="customer"
            )
            
            # Create dialogue records from segments
            for i, segment in enumerate(transcription_result['segments']):
                # Simple speaker assignment (alternating for now)
                # This will be replaced with proper speaker diarization
                speaker = agent_speaker if i % 2 == 0 else customer_speaker
                
                Dialogue.objects.create(
                    transcript=transcript,
                    speaker=speaker,
                    text=segment['text'].strip(),
                    start_time=segment['start'],
                    end_time=segment['end']
                )
            
            # Mark audio file as processed
            audio_file_instance.processed = True
            audio_file_instance.save()
            
            return transcript
            
        except Exception as e:
            raise Exception(f"Error processing audio file: {str(e)}")

class SpeakerDiarization:
    """
    Speaker diarization using pyannote.audio
    Note: This is a placeholder for now as pyannote requires additional setup
    """
    
    def __init__(self):
        # This will be implemented when pyannote is properly configured
        pass
    
    def diarize_speakers(self, audio_path):
        """
        Perform speaker diarization on audio file
        Returns speaker segments with timestamps
        """
        # Placeholder implementation
        # In a real implementation, this would use pyannote.audio
        return [
            {"speaker": "SPEAKER_00", "start": 0.0, "end": 10.0},
            {"speaker": "SPEAKER_01", "start": 10.0, "end": 20.0},
        ]
    
    def assign_speaker_roles(self, speaker_segments):
        """
        Assign roles (agent, customer, broker) to speakers
        This is a simplified implementation
        """
        role_mapping = {
            "SPEAKER_00": "agent",
            "SPEAKER_01": "customer",
            "SPEAKER_02": "broker"
        }
        
        assigned_segments = []
        for segment in speaker_segments:
            role = role_mapping.get(segment["speaker"], "unknown")
            assigned_segments.append({
                **segment,
                "role": role
            })
        
        return assigned_segments

