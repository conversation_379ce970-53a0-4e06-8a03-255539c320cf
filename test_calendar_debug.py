#!/usr/bin/env python3
"""
Debug calendar event creation issue
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_calendar_creation_debug():
    """Test calendar event creation with debugging"""
    print("🔍 DEBUGGING CALENDAR EVENT CREATION")
    print("=" * 50)
    
    try:
        # Get latest PII data
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    print(f"📊 PII Data for Calendar Event:")
                    print(f"   ID: {pii_id}")
                    print(f"   Names: {pii_data.get('names', [])}")
                    print(f"   Emails: {pii_data.get('emails', [])}")
                    print(f"   Dates: {pii_data.get('appointment_dates', [])}")
                    print(f"   Times: {pii_data.get('appointment_times', [])}")
                    
                    # Try to create calendar event
                    print(f"\n📅 Creating calendar event...")
                    response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                           json={"pii_data_id": pii_id},
                                           headers={'Content-Type': 'application/json'})
                    
                    print(f"Response status: {response.status_code}")
                    
                    if response.status_code == 200:
                        data = response.json()
                        print(f"✅ Calendar event created successfully!")
                        print(f"   Event details: {data}")
                    else:
                        print(f"❌ Calendar event creation failed")
                        try:
                            error_data = response.json()
                            print(f"   Error: {error_data.get('error', 'Unknown error')}")
                        except:
                            print(f"   Raw response: {response.text}")
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 CALENDAR EVENT CREATION DEBUG")
    print("=" * 60)
    
    test_calendar_creation_debug()
    
    print("\n" + "=" * 60)
    print("✨ DEBUG COMPLETED")
    print("=" * 60)
