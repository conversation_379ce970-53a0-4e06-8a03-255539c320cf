# Generated by Django 5.2.2 on 2025-06-07 16:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('audio_processing', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PIIData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('names', models.<PERSON><PERSON><PERSON>ield(default=list)),
                ('phone_numbers', models.J<PERSON><PERSON>ield(default=list)),
                ('emails', models.JSONField(default=list)),
                ('addresses', models.J<PERSON>NField(default=list)),
                ('appointment_dates', models.J<PERSON><PERSON>ield(default=list)),
                ('appointment_times', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('transcript', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pii_data', to='audio_processing.transcript')),
            ],
        ),
        migrations.CreateModel(
            name='GoogleSheetsEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sheet_id', models.CharField(max_length=255)),
                ('row_number', models.IntegerField()),
                ('processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pii_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sheet_entries', to='pii_extraction.piidata')),
            ],
        ),
    ]
