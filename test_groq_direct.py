#!/usr/bin/env python3
"""
Direct test of Groq client to verify it's working
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

def test_groq_direct():
    """Test Groq client directly"""
    print("🧠 TESTING GROQ CLIENT DIRECTLY")
    print("=" * 50)
    
    try:
        from groq import Groq
        
        # Initialize client
        api_key = os.getenv('GROQ_API_KEY')
        if not api_key:
            print("❌ No GROQ_API_KEY found")
            return
        
        print(f"🔑 Using API key: {api_key[:10]}...{api_key[-5:]}")
        
        client = Groq(api_key=api_key)
        print("✅ Groq client initialized successfully")
        
        # Test simple prompt
        test_transcript = "Hi, is this <PERSON>? Yes, speaking. How can I help you? I'm calling regarding the house listing."
        
        prompt = f"""
Extract the **specific reason** the customer contacted support from the transcript.
Focus on the issue, request, or concern. Provide a concise summary in one sentence.
Transcript:
"{test_transcript}"
        """
        
        print("🧠 Testing LLM call...")
        response = client.chat.completions.create(
            model="llama3-8b-8192",  # Updated model
            messages=[{"role": "user", "content": prompt}]
        )
        
        result = response.choices[0].message.content.strip()
        print(f"✅ LLM response successful!")
        print(f"   Response: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Groq test failed: {str(e)}")
        return False

def test_ira_service_direct():
    """Test IRA service directly"""
    print("\n🔧 TESTING IRA SERVICE DIRECTLY")
    print("=" * 50)
    
    try:
        from ira_analysis.services import IRAAnalyzer
        
        # Initialize analyzer
        analyzer = IRAAnalyzer()
        print("✅ IRA analyzer initialized")
        
        # Test with sample transcript
        test_transcript = "Hi, is this John? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is Jane and my email <NAME_EMAIL>."
        
        print("🧠 Processing transcript...")
        results = analyzer.process_transcript(test_transcript)
        
        print("✅ Processing completed!")
        print("📊 Results:")
        for key, value in results.items():
            if isinstance(value, str) and len(value) > 100:
                print(f"   {key}: {value[:100]}...")
            else:
                print(f"   {key}: {value}")
        
        return results
        
    except Exception as e:
        print(f"❌ IRA service test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_with_real_transcript():
    """Test with real transcript from database"""
    print("\n📄 TESTING WITH REAL TRANSCRIPT")
    print("=" * 50)
    
    try:
        from audio_processing.models import Transcript
        from ira_analysis.services import IRAAnalyzer
        
        # Get latest transcript
        transcript = Transcript.objects.order_by('-created_at').first()
        if not transcript:
            print("❌ No transcripts found")
            return
        
        print(f"📄 Using transcript ID: {transcript.id}")
        print(f"   Text preview: {transcript.text[:150]}...")
        
        # Initialize analyzer
        analyzer = IRAAnalyzer()
        
        # Generate report
        print("🧠 Generating IRA report...")
        report = analyzer.generate_ira_report(transcript.id)
        
        print("✅ Report generated successfully!")
        print(f"   Report ID: {report.id}")
        print(f"   Summary: {report.summary[:100]}...")
        print(f"   Sentiment Score: {report.sentiment_score}")
        print(f"   Report Text: {report.report_text[:200]}...")
        
        return report
        
    except Exception as e:
        print(f"❌ Real transcript test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("🚀 DIRECT GROQ AND IRA TESTING")
    print("=" * 60)
    
    # Test Groq client directly
    groq_works = test_groq_direct()
    
    if groq_works:
        # Test IRA service
        ira_results = test_ira_service_direct()
        
        if ira_results:
            # Test with real transcript
            real_report = test_with_real_transcript()
    
    print("\n" + "=" * 60)
    print("✨ DIRECT TESTING COMPLETED")
    print("=" * 60)
