#!/usr/bin/env python3
"""
Test script to verify the specific PII extraction fixes for the user's transcript
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

from pii_extraction.enhanced_services import EnhancedTranscriptCleaner, EnhancedPIIExtractor

def test_specific_transcript():
    """Test the specific transcript from the user"""
    print("🧪 TESTING SPECIFIC TRANSCRIPT FIXES")
    print("=" * 60)
    
    # The actual transcript text from the user
    transcript_text = """Hi, is this <PERSON>? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is <PERSON> and my email ID is <PERSON><PERSON>@email.Com. Nice to meet you, <PERSON>. When would you to share dual and appointment to see the house? How about 5th December? Okay, if December works for now, I'll load it down. , after checking my calendar, can we move it to the 7th December instead? No problem. Let's finalize it to 7th December. Then I'll update my records. Perfect. Thank you."""
    
    print("Original transcript:")
    print(transcript_text[:200] + "...")
    print()
    
    # Test transcript cleaning
    cleaner = EnhancedTranscriptCleaner()
    cleaned_text = cleaner.clean_transcript(transcript_text)
    
    print("Cleaned transcript:")
    print(cleaned_text[:200] + "...")
    print()
    
    # Check specific fixes
    print("Cleaning fixes:")
    if "<EMAIL>" in cleaned_text.lower():
        print("  ✅ Fixed email case: <EMAIL> → <EMAIL>")
    else:
        print("  ❌ Email case not fixed")
        print(f"     Looking for email in: {cleaned_text}")
    
    print()

def test_name_extraction():
    """Test name extraction on the specific transcript"""
    print("👤 TESTING NAME EXTRACTION")
    print("=" * 60)
    
    transcript_text = """Hi, is this John? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is Jane and my email <NAME_EMAIL>. Nice to meet you, Jane. When would you to share dual and appointment to see the house? How about 5th December?"""
    
    extractor = EnhancedPIIExtractor(use_groq=False)
    names = extractor.extract_names(transcript_text)
    
    print("Extracted names:")
    print(names)
    print()
    
    # Expected names: John, Jane
    expected_names = ["John", "Jane"]
    unwanted_phrases = ["Jane and my email ID is Jane", "calling regarding the house listing"]
    
    print("Name extraction evaluation:")
    for expected in expected_names:
        found = any(expected in name for name in names if name != "Not Found")
        if found:
            print(f"  ✅ Found expected name: {expected}")
        else:
            print(f"  ❌ Missing expected name: {expected}")
    
    for unwanted in unwanted_phrases:
        found = any(unwanted in name for name in names if name != "Not Found")
        if found:
            print(f"  ❌ Found unwanted phrase: {unwanted}")
        else:
            print(f"  ✅ Correctly filtered out unwanted phrase")
    
    print()

def test_email_extraction():
    """Test email extraction on the specific transcript"""
    print("📧 TESTING EMAIL EXTRACTION")
    print("=" * 60)
    
    transcript_text = """My name is Jane and my email <NAME_EMAIL>. Nice to meet you, Jane."""
    
    extractor = EnhancedPIIExtractor(use_groq=False)
    emails = extractor.extract_by_pattern(transcript_text, 'email')
    
    print("Extracted emails:")
    print(emails)
    print()
    
    print("Email extraction evaluation:")
    if emails and emails != ["Not Found"]:
        for email in emails:
            if "<EMAIL>" in email.lower():
                print(f"  ✅ Found correct email: {email}")
            elif "@" in email and "." in email:
                print(f"  ⚠️  Found email but case might be wrong: {email}")
            else:
                print(f"  ❌ Invalid email format: {email}")
    else:
        print("  ❌ No emails found")
    
    print()

def test_date_extraction():
    """Test date extraction on the specific transcript"""
    print("📅 TESTING DATE EXTRACTION")
    print("=" * 60)
    
    transcript_text = """How about 5th December? Okay, if December works for now, I'll load it down. , after checking my calendar, can we move it to the 7th December instead? No problem. Let's finalize it to 7th December."""
    
    extractor = EnhancedPIIExtractor(use_groq=False)
    dates = extractor.extract_by_pattern(transcript_text, 'date')
    
    print("Extracted dates:")
    print(dates)
    print()
    
    expected_dates = ["5th December", "7th December"]
    
    print("Date extraction evaluation:")
    if dates and dates != ["Not Found"]:
        for expected in expected_dates:
            found = any(expected in date for date in dates)
            if found:
                print(f"  ✅ Found expected date: {expected}")
            else:
                print(f"  ❌ Missing expected date: {expected}")
    else:
        print("  ❌ No dates found")
    
    print()

def test_full_extraction():
    """Test full PII extraction on the complete transcript"""
    print("🔍 TESTING FULL PII EXTRACTION")
    print("=" * 60)
    
    transcript_text = """Hi, is this John? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is Jane and my email <NAME_EMAIL>. Nice to meet you, Jane. When would you to share dual and appointment to see the house? How about 5th December? Okay, if December works for now, I'll load it down. , after checking my calendar, can we move it to the 7th December instead? No problem. Let's finalize it to 7th December. Then I'll update my records. Perfect. Thank you."""
    
    extractor = EnhancedPIIExtractor(use_groq=False)
    
    # Test individual extractions
    names = extractor.extract_names(transcript_text)
    emails = extractor.extract_by_pattern(transcript_text, 'email')
    dates = extractor.extract_by_pattern(transcript_text, 'date')
    
    print("Complete extraction results:")
    print(f"  Names: {names}")
    print(f"  Emails: {emails}")
    print(f"  Dates: {dates}")
    print()
    
    print("Overall evaluation:")
    
    # Names evaluation
    if any("John" in name for name in names if name != "Not Found"):
        print("  ✅ Found John")
    else:
        print("  ❌ Missing John")
    
    if any("Jane" in name for name in names if name != "Not Found"):
        print("  ✅ Found Jane")
    else:
        print("  ❌ Missing Jane")
    
    # Check for unwanted long phrases
    long_phrases = [name for name in names if name != "Not Found" and len(name.split()) > 3]
    if not long_phrases:
        print("  ✅ No unwanted long phrases in names")
    else:
        print(f"  ❌ Found unwanted long phrases: {long_phrases}")
    
    # Email evaluation
    if any("<EMAIL>" in email.lower() for email in emails if email != "Not Found"):
        print("  ✅ Found correct email format")
    else:
        print("  ❌ Email format issue")
    
    # Date evaluation
    if any("5th December" in date for date in dates if date != "Not Found"):
        print("  ✅ Found 5th December")
    else:
        print("  ❌ Missing 5th December")
    
    if any("7th December" in date for date in dates if date != "Not Found"):
        print("  ✅ Found 7th December")
    else:
        print("  ❌ Missing 7th December")

if __name__ == "__main__":
    print("🚀 SPECIFIC TRANSCRIPT FIXES TEST")
    print("=" * 60)
    print("Testing fixes for the user's specific transcript issues")
    print()
    
    # Test transcript cleaning
    test_specific_transcript()
    
    # Test name extraction
    test_name_extraction()
    
    # Test email extraction
    test_email_extraction()
    
    # Test date extraction
    test_date_extraction()
    
    # Test full extraction
    test_full_extraction()
    
    print("=" * 60)
    print("✨ SPECIFIC FIXES TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Expected improvements:")
    print("• Names: Should extract 'John' and 'Jane', not long phrases")
    print("• Emails: Should extract '<EMAIL>' (normalized case)")
    print("• Dates: Should extract '5th December' and '7th December'")
    print("• No more unwanted phrases like 'calling regarding the house listing'")
    print("\n🎯 These fixes should resolve the specific issues you reported!")
