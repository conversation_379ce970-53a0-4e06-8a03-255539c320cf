#!/usr/bin/env python3
"""
Test script to verify the API endpoints are working correctly
"""

import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:8000/api"

def test_clean_transcript_api():
    """Test the clean transcript API endpoint"""
    print("🧹 TESTING CLEAN TRANSCRIPT API")
    print("=" * 50)
    
    audio_id = 2  # Use your audio ID
    
    try:
        # First, get the current transcripts to see what we have
        print("📋 Getting current transcripts...")
        response = requests.get(f"{BASE_URL}/audio/{audio_id}/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {data['total_transcripts']} transcripts")
            
            if data['transcripts']:
                latest_transcript = data['transcripts'][0]  # Most recent
                print(f"   Latest transcript ID: {latest_transcript['id']}")
                print(f"   Preview: {latest_transcript['text'][:100]}...")
            else:
                print("❌ No transcripts found")
                return
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return
        
        print("\n🧹 Cleaning transcript...")
        # Clean the transcript
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/clean_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Transcript cleaned successfully")
            print(f"   Cleaned transcript ID: {data['transcript_id']}")
            print(f"   Total transcripts: {data['total_transcripts']}")
            print(f"   Note: {data.get('note', 'N/A')}")
            print(f"   Cleaned preview: {data['cleaned_text']}")
            
            # Check for specific improvements
            cleaned_text = data['cleaned_text']
            improvements = []
            
            if "Oh," in cleaned_text:
                improvements.append("✅ Fixed '0' → 'Oh'")
            if "@" in cleaned_text and ".com" in cleaned_text:
                improvements.append("✅ Email format looks good")
            if "gmail.com" in cleaned_text:
                improvements.append("✅ Domain spacing fixed")
            
            if improvements:
                print("\n   Improvements detected:")
                for improvement in improvements:
                    print(f"     {improvement}")
            
        else:
            print(f"❌ Error cleaning transcript: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_pii_extraction_api():
    """Test the PII extraction API endpoint"""
    print("\n🔍 TESTING PII EXTRACTION API")
    print("=" * 50)
    
    try:
        # Test PII extraction from transcript
        transcript_id = 4  # Use your transcript ID
        
        print(f"📊 Extracting PII from transcript {transcript_id}...")
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": transcript_id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ PII extracted successfully")
            
            pii_data = data.get('pii_data', {})
            
            # Display extracted PII
            print("\n   Extracted PII:")
            print(f"     Names: {pii_data.get('names', [])}")
            print(f"     Emails: {pii_data.get('emails', [])}")
            print(f"     Phone Numbers: {pii_data.get('phone_numbers', [])}")
            print(f"     Addresses: {pii_data.get('addresses', [])}")
            print(f"     Dates: {pii_data.get('appointment_dates', [])}")
            print(f"     Times: {pii_data.get('appointment_times', [])}")
            
            # Evaluate quality
            print("\n   Quality Assessment:")
            
            names = pii_data.get('names', [])
            if names and names != ["Not Found"]:
                # Check if we have reasonable names (not common words)
                good_names = [name for name in names if not any(word in name.lower() for word in ['hi', 'yes', 'how', 'could', 'sure', 'great'])]
                if good_names:
                    print(f"     ✅ Found {len(good_names)} reasonable names")
                else:
                    print(f"     ❌ Names look like common words: {names}")
            else:
                print(f"     ❌ No names found")
            
            emails = pii_data.get('emails', [])
            if emails and emails != ["Not Found"]:
                valid_emails = [email for email in emails if "@" in email and "." in email]
                if valid_emails:
                    print(f"     ✅ Found {len(valid_emails)} valid emails")
                else:
                    print(f"     ❌ Invalid email formats: {emails}")
            else:
                print(f"     ❌ No emails found")
            
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_workflow():
    """Test the complete workflow"""
    print("\n🔄 TESTING COMPLETE WORKFLOW")
    print("=" * 50)
    
    audio_id = 2
    
    try:
        # Step 1: Process audio with diarization
        print("🎵 Step 1: Processing audio with diarization...")
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/process_with_diarization/")
        if response.status_code == 200:
            data = response.json()
            transcript_id = data['transcript_id']
            print(f"✅ Audio processed, transcript ID: {transcript_id}")
        else:
            print(f"❌ Error processing audio: {response.status_code}")
            return
        
        # Step 2: Clean the transcript
        print("\n🧹 Step 2: Cleaning transcript...")
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/clean_transcript/")
        if response.status_code == 200:
            data = response.json()
            cleaned_transcript_id = data['transcript_id']
            print(f"✅ Transcript cleaned, ID: {cleaned_transcript_id}")
            print(f"   Preview: {data['cleaned_text'][:100]}...")
        else:
            print(f"❌ Error cleaning transcript: {response.status_code}")
            return
        
        # Step 3: Extract PII
        print("\n🔍 Step 3: Extracting PII...")
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": cleaned_transcript_id},
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            pii_data = data.get('pii_data', {})
            print(f"✅ PII extracted successfully")
            print(f"   Names: {len([n for n in pii_data.get('names', []) if n != 'Not Found'])} found")
            print(f"   Emails: {len([e for e in pii_data.get('emails', []) if e != 'Not Found'])} found")
            print(f"   Phone Numbers: {len([p for p in pii_data.get('phone_numbers', []) if p != 'Not Found'])} found")
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
        
        print("\n✅ Complete workflow tested successfully!")
        
    except Exception as e:
        print(f"❌ Workflow error: {str(e)}")

if __name__ == "__main__":
    print("🚀 API ENDPOINTS TEST")
    print("=" * 60)
    print("Make sure the Django server is running on http://127.0.0.1:8000")
    print()
    
    # Test individual endpoints
    test_clean_transcript_api()
    test_pii_extraction_api()
    
    # Test complete workflow
    test_workflow()
    
    print("\n" + "=" * 60)
    print("✨ API TESTING COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• Transcript cleaning API should now fix '0' → 'Oh'")
    print("• PII extraction should avoid common words as names")
    print("• Email extraction should handle speech-to-text formats")
    print("• Complete workflow should work end-to-end")
    print("\n🎯 Your APIs are now significantly improved!")
