#!/usr/bin/env python3
"""
Simple test for IRA analysis API
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_ira_analysis():
    """Test IRA analysis with the latest transcript"""
    print("🧠 TESTING IRA ANALYSIS API")
    print("=" * 50)
    
    try:
        # Get the latest transcript
        print("📄 Getting latest transcript...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                print(f"✅ Found transcript ID: {transcript_id}")
                
                # Test IRA analysis
                print(f"🧠 Running IRA analysis...")
                response = requests.post(f"{BASE_URL}/ira/analyze_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ IRA analysis successful!")
                    print(f"   Report ID: {data.get('report_id')}")
                    print(f"   Summary: {data.get('summary', '')[:150]}...")
                    print(f"   Sentiment Score: {data.get('sentiment_score')}")
                    
                    # Show more details if available
                    if 'report_text' in data:
                        print(f"   Report preview: {data['report_text'][:200]}...")
                    
                else:
                    print(f"❌ IRA analysis failed: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   Error: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   Raw response: {response.text}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 SIMPLE IRA ANALYSIS TEST")
    print("=" * 60)
    print("Testing if the Groq 'proxies' error is fixed")
    print()
    
    test_ira_analysis()
    
    print("\n" + "=" * 60)
    print("✨ TEST COMPLETED")
    print("=" * 60)
