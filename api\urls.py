from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import (
    AudioProcessingViewSet,
    PIIExtractionViewSet,
    IRAAnalysisViewSet,
    CalendarSchedulerViewSet,
    UtilityViewSet
)

router = DefaultRouter()
router.register(r'audio', AudioProcessingViewSet, basename='audio')
router.register(r'pii', PIIExtractionViewSet, basename='pii')
router.register(r'ira', IRAAnalysisViewSet, basename='ira')
router.register(r'calendar', CalendarSchedulerViewSet, basename='calendar')
router.register(r'utility', UtilityViewSet, basename='utility')

urlpatterns = [
    path('', include(router.urls)),
]

