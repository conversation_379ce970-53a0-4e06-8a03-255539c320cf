# Generated by Django 5.2.2 on 2025-06-08 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pii_extraction', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='piidata',
            name='confidence_scores',
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name='piidata',
            name='credit_cards',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='piidata',
            name='ssn',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='piidata',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='PIIValidation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('validation_results', models.J<PERSON><PERSON>ield(default=dict)),
                ('overall_confidence', models.<PERSON><PERSON><PERSON>ield(default=0.0)),
                ('validated_at', models.DateTimeField(auto_now_add=True)),
                ('pii_data', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='validation', to='pii_extraction.piidata')),
            ],
        ),
    ]
