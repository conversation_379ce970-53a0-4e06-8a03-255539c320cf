#!/usr/bin/env python3
"""
Test script to verify the PII extraction and transcript cleaning fixes
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

from pii_extraction.enhanced_services import EnhancedTranscriptCleaner, EnhancedPIIExtractor

def test_transcript_cleaning():
    """Test the enhanced transcript cleaning"""
    print("🧹 TESTING TRANSCRIPT CLEANING FIXES")
    print("=" * 60)
    
    cleaner = EnhancedTranscriptCleaner()
    
    # Test case that matches your actual transcript
    test_transcript = "Hi, is this <PERSON>? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. 0, great. Could you share a few details with me? Sure. My name is <PERSON> and my email ID is jane at the rate gmail dot com"
    
    print("Original transcript:")
    print(test_transcript)
    print()
    
    cleaned = cleaner.clean_transcript(test_transcript)
    
    print("Cleaned transcript:")
    print(cleaned)
    print()
    
    # Check specific fixes
    fixes_applied = []
    if "Oh, great" in cleaned:
        fixes_applied.append("✅ Fixed '0' → 'Oh'")
    else:
        fixes_applied.append("❌ '0' → 'Oh' fix not applied")
    
    if "<EMAIL>" in cleaned:
        fixes_applied.append("✅ Fixed email format")
    else:
        fixes_applied.append("❌ Email format not fixed")
    
    print("Fixes applied:")
    for fix in fixes_applied:
        print(f"  {fix}")

def test_name_extraction():
    """Test the enhanced name extraction"""
    print("\n👤 TESTING NAME EXTRACTION FIXES")
    print("=" * 60)
    
    extractor = EnhancedPIIExtractor(use_groq=False)  # Test without Groq first
    
    # Test case that matches your actual transcript
    test_text = "Hi, is this John? Yes, speaking. How can I help you? I'm calling regarding the house listing. I saw online. Oh, great. Could you share a few details with me? Sure. My name is Jane and my email <NAME_EMAIL>"
    
    print("Test text:")
    print(test_text)
    print()
    
    names = extractor.extract_names(test_text)
    
    print("Extracted names:")
    print(names)
    print()
    
    # Check if we got the right names
    expected_names = ["John", "Jane"]
    unwanted_names = ["Hi", "Yes", "How", "Could", "Sure"]
    
    results = []
    for expected in expected_names:
        if any(expected in name for name in names if name != "Not Found"):
            results.append(f"✅ Found expected name: {expected}")
        else:
            results.append(f"❌ Missing expected name: {expected}")
    
    for unwanted in unwanted_names:
        if any(unwanted in name for name in names if name != "Not Found"):
            results.append(f"❌ Found unwanted name: {unwanted}")
        else:
            results.append(f"✅ Correctly filtered out: {unwanted}")
    
    print("Name extraction results:")
    for result in results:
        print(f"  {result}")

def test_email_extraction():
    """Test the enhanced email extraction"""
    print("\n📧 TESTING EMAIL EXTRACTION FIXES")
    print("=" * 60)
    
    extractor = EnhancedPIIExtractor(use_groq=False)  # Test without Groq first
    
    # Test cases with various email formats
    test_cases = [
        "My email ID is jane at the rate gmail dot com",
        "You can reach me at john dot smith at dred yahoo dot com",
        "Contact me at sarah at rate hotmail dot com",
        "My <NAME_EMAIL>"  # Already correct format
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"Test {i}: {test_text}")
        
        emails = extractor.extract_by_pattern(test_text, 'email')
        print(f"Extracted: {emails}")
        
        # Check if we got a valid email
        if emails and emails != ["Not Found"]:
            for email in emails:
                if "@" in email and "." in email:
                    print(f"  ✅ Valid email format: {email}")
                else:
                    print(f"  ❌ Invalid email format: {email}")
        else:
            print(f"  ❌ No email extracted")
        print("-" * 30)

def test_full_pii_extraction():
    """Test full PII extraction on sample transcript"""
    print("\n🔍 TESTING FULL PII EXTRACTION")
    print("=" * 60)
    
    # Create a sample transcript in the database for testing
    from audio_processing.models import Transcript, AudioFile
    
    try:
        # Get an existing audio file or create one for testing
        audio_file = AudioFile.objects.first()
        if not audio_file:
            print("❌ No audio file found in database for testing")
            return
        
        # Create a test transcript with better content
        test_content = """
        Hi, is this John Smith? Yes, speaking. How can I help you? 
        I'm calling regarding the house listing I saw online. Oh, great! 
        Could you share a few details with me? Sure. My name is Jane Doe 
        and my email ID is jane dot doe at the rate gmail dot com. 
        You can also call me at five five five one two three four five six seven.
        I live at 123 Main Street, Apartment 4B, New York 10001.
        I'm available for viewing next Monday at 2:30 PM.
        """
        
        # Create or update transcript
        transcript, created = Transcript.objects.get_or_create(
            audio=audio_file,
            defaults={'text': test_content}
        )
        
        if not created:
            transcript.text = test_content
            transcript.save()
        
        print(f"Using transcript ID: {transcript.id}")
        print("Transcript content:")
        print(test_content[:200] + "...")
        print()
        
        # Extract PII
        extractor = EnhancedPIIExtractor(use_groq=False)
        pii_data = extractor.extract_pii_from_transcript(transcript.id)
        
        print("Extracted PII:")
        print(f"  Names: {pii_data.names}")
        print(f"  Emails: {pii_data.emails}")
        print(f"  Phone Numbers: {pii_data.phone_numbers}")
        print(f"  Addresses: {pii_data.addresses}")
        print(f"  Dates: {pii_data.appointment_dates}")
        print(f"  Times: {pii_data.appointment_times}")
        
        # Evaluate results
        print("\nEvaluation:")
        
        # Check names
        if "Jane Doe" in str(pii_data.names) or "John Smith" in str(pii_data.names):
            print("  ✅ Found proper names")
        else:
            print("  ❌ Missing proper names")
        
        # Check emails
        if "<EMAIL>" in str(pii_data.emails) or any("@" in email for email in pii_data.emails if email != "Not Found"):
            print("  ✅ Found email addresses")
        else:
            print("  ❌ Missing email addresses")
        
        # Check phone numbers
        if any("555" in phone for phone in pii_data.phone_numbers if phone != "Not Found"):
            print("  ✅ Found phone numbers")
        else:
            print("  ❌ Missing phone numbers")
            
    except Exception as e:
        print(f"❌ Error in full PII extraction test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 PII EXTRACTION AND CLEANING FIXES TEST")
    print("=" * 60)
    
    # Test transcript cleaning
    test_transcript_cleaning()
    
    # Test name extraction
    test_name_extraction()
    
    # Test email extraction
    test_email_extraction()
    
    # Test full PII extraction
    test_full_pii_extraction()
    
    print("\n" + "=" * 60)
    print("✨ TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary of fixes:")
    print("• Fixed '0' → 'Oh' conversion in transcript cleaning")
    print("• Enhanced name extraction to avoid common words")
    print("• Added speech-to-text email pattern recognition")
    print("• Improved context-based name detection")
    print("• Better filtering of false positive names")
    print("\n🎯 The PII extraction should now work much better!")
