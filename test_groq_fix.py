#!/usr/bin/env python3
"""
Test script to verify the Groq client fix
"""

import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

BASE_URL = "http://127.0.0.1:8000/api"

def test_groq_client_initialization():
    """Test Groq client initialization directly"""
    print("🔧 TESTING GROQ CLIENT INITIALIZATION")
    print("=" * 50)
    
    # Test PII extraction Groq client
    print("📊 Testing PII extraction Groq client...")
    try:
        from pii_extraction.enhanced_services import GroqPIIExtractor
        
        groq_extractor = GroqPIIExtractor()
        print("✅ PII extraction Groq client initialized successfully")
        
        # Test a simple extraction
        test_text = "Hi, my name is <PERSON> and my <NAME_EMAIL>"
        result = groq_extractor.extract_pii_with_groq(test_text)
        print(f"✅ PII extraction test successful: {result.get('names', [])}")
        
    except Exception as e:
        print(f"❌ PII extraction Groq client failed: {str(e)}")
    
    # Test IRA analysis Groq client
    print("\n🧠 Testing IRA analysis Groq client...")
    try:
        from ira_analysis.services import GroqClient
        
        groq_client = GroqClient()
        print("✅ IRA analysis Groq client initialized successfully")
        
        # Test a simple LLM call
        test_prompt = "Summarize this in one sentence: Customer called about billing issue"
        result = groq_client.get_llm_response(test_prompt)
        print(f"✅ IRA analysis test successful: {result[:100]}...")
        
    except Exception as e:
        print(f"❌ IRA analysis Groq client failed: {str(e)}")

def test_ira_analysis_api():
    """Test the IRA analysis API endpoint"""
    print("\n🧠 TESTING IRA ANALYSIS API")
    print("=" * 50)
    
    try:
        # Get the latest transcript
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                print(f"📄 Using transcript ID: {transcript_id}")
                
                # Test IRA analysis
                print(f"🧠 Running IRA analysis...")
                response = requests.post(f"{BASE_URL}/ira/analyze_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ IRA analysis successful!")
                    print(f"   Report ID: {data.get('report_id')}")
                    print(f"   Summary: {data.get('summary', '')[:100]}...")
                    print(f"   Sentiment Score: {data.get('sentiment_score')}")
                else:
                    print(f"❌ IRA analysis failed: {response.status_code}")
                    print(f"   Response: {response.text}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
    except Exception as e:
        print(f"❌ IRA analysis API error: {str(e)}")

def check_groq_api_key():
    """Check if Groq API key is properly configured"""
    print("\n🔑 CHECKING GROQ API KEY")
    print("=" * 50)
    
    groq_api_key = os.getenv('GROQ_API_KEY')
    if groq_api_key:
        print(f"✅ GROQ_API_KEY is set")
        print(f"   Key preview: {groq_api_key[:10]}...{groq_api_key[-5:]}")
    else:
        print("❌ GROQ_API_KEY is not set")
        print("   Please set your Groq API key in environment variables")
        print("   Example: export GROQ_API_KEY='your_api_key_here'")
        return False
    
    return True

def test_groq_package_version():
    """Check Groq package version"""
    print("\n📦 CHECKING GROQ PACKAGE VERSION")
    print("=" * 50)
    
    try:
        import groq
        version = getattr(groq, '__version__', 'Unknown')
        print(f"✅ Groq package version: {version}")
        
        # Check if it's a compatible version
        if version != 'Unknown':
            major, minor = version.split('.')[:2]
            if int(major) >= 0 and int(minor) >= 4:
                print("✅ Version appears compatible")
            else:
                print("⚠️  Version might be outdated, consider upgrading")
                print("   Run: pip install --upgrade groq")
        
    except ImportError:
        print("❌ Groq package not installed")
        print("   Run: pip install groq")
    except Exception as e:
        print(f"⚠️  Could not determine version: {str(e)}")

def main():
    print("🚀 GROQ CLIENT FIX TEST")
    print("=" * 60)
    print("Testing the Groq client initialization fixes")
    print()
    
    # Check prerequisites
    if not check_groq_api_key():
        print("\n❌ Cannot proceed without Groq API key")
        return
    
    # Check package version
    test_groq_package_version()
    
    # Test client initialization
    test_groq_client_initialization()
    
    # Test API endpoint
    test_ira_analysis_api()
    
    print("\n" + "=" * 60)
    print("✨ GROQ CLIENT TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• If initialization succeeded, the 'proxies' error should be fixed")
    print("• If IRA analysis worked, the API endpoint is functional")
    print("• If there are still errors, check the Groq API key and package version")
    print("\n🎯 The Groq client should now work without the 'proxies' error!")

if __name__ == "__main__":
    main()
