# Generated by Django 5.2.2 on 2025-06-07 16:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AudioFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to='audio/')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
                ('processed', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Transcript',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('audio', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transcripts', to='audio_processing.audiofile')),
            ],
        ),
        migrations.CreateModel(
            name='Speaker',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('transcript', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='speakers', to='audio_processing.transcript')),
            ],
        ),
        migrations.CreateModel(
            name='Dialogue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('start_time', models.FloatField()),
                ('end_time', models.FloatField()),
                ('speaker', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dialogues', to='audio_processing.speaker')),
                ('transcript', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='dialogues', to='audio_processing.transcript')),
            ],
        ),
    ]
