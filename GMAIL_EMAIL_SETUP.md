# Gmail Email Notifications Setup Guide

## 🔍 **Current Issue**

Your notification system shows "email sent" but you're not receiving emails because it's using **MOCK/FAKE** email service that only prints to console.

## ✅ **Fixed with Your Code**

I've updated the email service to use your simple and reliable Gmail SMTP implementation. Now it will send **REAL** emails when properly configured.

## 🔧 **Setup Steps**

### **Step 1: Enable Gmail App Password**

1. **Go to your Gmail account**
2. **Click on your profile picture** → "Manage your Google Account"
3. **Go to Security tab**
4. **Enable 2-Step Verification** (if not already enabled)
5. **Go to App passwords**:
   - Search for "App passwords" in the search bar
   - Or go to: https://myaccount.google.com/apppasswords
6. **Generate App Password**:
   - Select app: "Mail"
   - Select device: "Windows Computer" (or your device)
   - Click "Generate"
7. **Copy the 16-character password** (e.g., `abcd efgh ijkl mnop`)

### **Step 2: Set Environment Variables**

**Windows Command Prompt:**
```cmd
set SENDER_EMAIL=<EMAIL>
set SENDER_PASSWORD=abcd efgh ijkl mnop
```

**Windows PowerShell:**
```powershell
$env:SENDER_EMAIL="<EMAIL>"
$env:SENDER_PASSWORD="abcd efgh ijkl mnop"
```

**Linux/Mac:**
```bash
export SENDER_EMAIL="<EMAIL>"
export SENDER_PASSWORD="abcd efgh ijkl mnop"
```

### **Step 3: Restart Django Server**

```bash
python manage.py runserver
```

## 🧪 **Testing Process**

### **Method 1: Complete Workflow Test**

1. **Run the test script**:
   ```bash
   python test_real_email_notifications.py
   ```

2. **Check the output** - it will show:
   - ✅ Email credentials configured
   - ✅ Real emails will be sent via Gmail SMTP
   - 📧 Check your email inbox for notifications

### **Method 2: Manual API Testing**

1. **Edit Google Sheets** with your real email address
2. **Create calendar event from sheet**:
   ```bash
   POST /api/calendar/create_event_from_pii/
   {"sheet_entry_id": 12}
   ```
3. **Send notifications**:
   ```bash
   POST /api/calendar/{event_id}/send_notifications/
   ```
4. **Check your email inbox**

## 📧 **Email Content**

When working, you'll receive an email like:

**Subject**: Appointment Confirmation

**Body**:
```
Dear [Customer Name],

Your appointment has been confirmed for [Date] at [Time].

Details:
- Name: [Customer Name]
- Email: [Customer Email]
- Phone: [Customer Phone]
- Date: [Appointment Date]
- Time: [Appointment Time]
- Location: [Address]

Please let us know if you need to reschedule.

Best regards,
Appointment Team
```

## 🔍 **Debugging**

### **Check Console Output**

When email is sent, you'll see:
```
📧 Attempting to send <NAME_EMAIL>
   Subject: Appointment Confirmation
   From: <EMAIL>
✅ Email sent <NAME_EMAIL>
```

### **If Email Fails**

You'll see:
```
❌ Email sending failed: [error message]
📧 FALLBACK - MOCK EMAIL:
   To: <EMAIL>
   Subject: Appointment Confirmation
   Body: [email content]
```

### **Common Issues**

1. **"Authentication failed"**:
   - Make sure you're using App Password, not regular password
   - Verify 2-Factor Authentication is enabled

2. **"SENDER_EMAIL not set"**:
   - Check environment variables are set correctly
   - Restart Django server after setting variables

3. **"Less secure app access"**:
   - Use App Password instead of regular password
   - Don't enable "Less secure app access" (deprecated)

## 🎯 **Environment Variables Summary**

You need these two environment variables:

```bash
SENDER_EMAIL=<EMAIL>        # Your Gmail address
SENDER_PASSWORD=abcd efgh ijkl mnop      # 16-character App Password (not regular password)
```

## 📱 **SMS Notifications**

SMS is still MOCK/FAKE. To enable real SMS:

1. **Sign up for Twilio** (free trial)
2. **Set environment variables**:
   ```bash
   SMS_PROVIDER=twilio
   TWILIO_ACCOUNT_SID=your-account-sid
   TWILIO_AUTH_TOKEN=your-auth-token
   TWILIO_PHONE_NUMBER=+**********
   ```

## ✅ **Verification Steps**

1. **Set Gmail credentials** ✅
2. **Restart Django server** ✅
3. **Edit Google Sheet** with your real email ✅
4. **Create calendar event** from sheet entry ✅
5. **Send notifications** ✅
6. **Check email inbox** ✅

## 🚀 **Quick Test Command**

After setting up Gmail credentials:

```bash
# Test the complete workflow
python test_real_email_notifications.py

# Or test manually:
# 1. POST /api/calendar/create_event_from_pii/ {"sheet_entry_id": 12}
# 2. POST /api/calendar/{event_id}/send_notifications/
# 3. Check your email!
```

## 💡 **Pro Tips**

1. **Use your real email** in Google Sheets for testing
2. **Check spam folder** if email doesn't appear in inbox
3. **App Password is different** from your regular Gmail password
4. **Environment variables** must be set before starting Django server
5. **Gmail SMTP is reliable** and free for moderate usage

Your email notifications will now be **REAL** instead of fake! 🎉
