from django.db import models

class AudioFile(models.Model):
    file = models.FileField(upload_to='audio/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    processed = models.BooleanField(default=False)
    
    def __str__(self):
        return f"Audio {self.id} - {self.uploaded_at}"

class Transcript(models.Model):
    audio = models.ForeignKey(AudioFile, on_delete=models.CASCADE, related_name='transcripts')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Transcript for Audio {self.audio.id}"

class Speaker(models.Model):
    transcript = models.ForeignKey(Transcript, on_delete=models.CASCADE, related_name='speakers')
    name = models.CharField(max_length=100)  # e.g., "agent", "customer", "broker"
    
    def __str__(self):
        return f"{self.name} in Transcript {self.transcript.id}"

class Dialogue(models.Model):
    transcript = models.Foreign<PERSON><PERSON>(Transcript, on_delete=models.CASCADE, related_name='dialogues')
    speaker = models.Foreign<PERSON><PERSON>(Speaker, on_delete=models.CASCADE, related_name='dialogues')
    text = models.TextField()
    start_time = models.FloatField()  # in seconds
    end_time = models.FloatField()  # in seconds
    
    def __str__(self):
        return f"{self.speaker.name}: {self.text[:30]}..."

