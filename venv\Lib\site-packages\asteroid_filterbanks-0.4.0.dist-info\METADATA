Metadata-Version: 2.1
Name: asteroid-filterbanks
Version: 0.4.0
Summary: Asteroid's filterbanks
Home-page: https://github.com/asteroid-team/asteroid-filterbanks
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
Description-Content-Type: text/markdown
Requires-Dist: numpy
Requires-Dist: torch (>=1.8.0)
Requires-Dist: typing-extensions
Provides-Extra: all
Requires-Dist: librosa ; extra == 'all'
Requires-Dist: scipy ; extra == 'all'

<div align="center">
<img src=".github/asteroid_logo_dark.png" width="50%">


[![PyPI Status](https://badge.fury.io/py/asteroid-filterbanks.svg)](https://badge.fury.io/py/asteroid-filterbanks)
[![Build Status](https://github.com/asteroid-team/asteroid-filterbanks/workflows/CI/badge.svg)](https://github.com/asteroid-team/asteroid-filterbanks/actions?query=workflow%3ACI+branch%3Amaster+event%3Apush)
[![codecov][codecov-badge]][codecov]
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![Documentation Status](https://img.shields.io/badge/docs-0.3.3-blue)](https://asteroid.readthedocs.io/en/v0.3.3/package_reference/filterbanks.html)
[![Latest Docs Status](https://github.com/mpariente/asteroid/workflows/Latest%20docs/badge.svg)](https://mpariente.github.io/asteroid/package_reference/filterbanks.html)


[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/asteroid-team/asteroid-filterbanks/pulls)
[![Python Versions](https://img.shields.io/pypi/pyversions/asteroid.svg)](https://pypi.org/project/asteroid/)
[![Slack][slack-badge]][slack-invite]

<h3 align="center">
<p>Asteroid Filterbanks.
</h3>
</div>

--------------------------------------------------------------------------------
## Install
Latest release using pip:
```bash
pip install asteroid-filterbanks
```

Dev install
```bash
git clone https://github.com/asteroid-team/asteroid-filterbanks
cd asteroid-filterbanks
pip install -e .["all"]
```

## Citing Asteroid
```BibTex
@inproceedings{Pariente2020Asteroid,
    title={Asteroid: the {PyTorch}-based audio source separation toolkit for researchers},
    author={Manuel Pariente and Samuele Cornell and Joris Cosentino and Sunit Sivasankaran and
            Efthymios Tzinis and Jens Heitkaemper and Michel Olvera and Fabian-Robert Stöter and
            Mathieu Hu and Juan M. Martín-Doñas and David Ditter and Ariel Frank and Antoine Deleforge
            and Emmanuel Vincent},
    year={2020},
    booktitle={Proc. Interspeech},
}
```

[comment]: <> (Badge)
[codecov-badge]: https://codecov.io/gh/mpariente/asteroid-filterbanks/branch/master/graph/badge.svg
[codecov]: https://codecov.io/gh/asteroid-team/asteroid-filterbanks
[slack-badge]: https://img.shields.io/badge/slack-chat-green.svg?logo=slack
[slack-invite]: https://join.slack.com/t/asteroid-dev/shared_invite/zt-cn9y85t3-QNHXKD1Et7qoyzu1Ji5bcA


