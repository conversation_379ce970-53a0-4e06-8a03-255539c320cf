# Email & SMS Notification Setup Guide

## 🔍 **Current Issue**

Your notification system shows "email sent" and "sms sent" but you're not receiving anything because it's using **FAKE/PLACEHOLDER** services that only print to console.

## ✅ **Fixed Implementation**

I've updated the code to support **REAL** email and SMS providers. Here's how to set them up:

## 📧 **Email Setup Options**

### **Option 1: Gmail SMTP (Easiest)**

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Set Environment Variables**:
   ```bash
   EMAIL_PROVIDER=smtp
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=your-app-password
   ```

### **Option 2: SendGrid (Professional)**

1. **Sign up** at https://sendgrid.com (free tier: 100 emails/day)
2. **Get API Key** from SendGrid dashboard
3. **Set Environment Variables**:
   ```bash
   EMAIL_PROVIDER=sendgrid
   SENDGRID_API_KEY=your-sendgrid-api-key
   SMTP_USERNAME=<EMAIL>
   ```

### **Option 3: AWS SES (Scalable)**

1. **Set up AWS SES** in AWS Console
2. **Verify your email domain**
3. **Set Environment Variables**:
   ```bash
   EMAIL_PROVIDER=ses
   AWS_ACCESS_KEY_ID=your-aws-access-key
   AWS_SECRET_ACCESS_KEY=your-aws-secret-key
   AWS_REGION=us-east-1
   SMTP_USERNAME=<EMAIL>
   ```

## 📱 **SMS Setup Options**

### **Option 1: Twilio (Most Popular)**

1. **Sign up** at https://twilio.com (free trial: $15 credit)
2. **Get phone number** from Twilio console
3. **Set Environment Variables**:
   ```bash
   SMS_PROVIDER=twilio
   TWILIO_ACCOUNT_SID=your-account-sid
   TWILIO_AUTH_TOKEN=your-auth-token
   TWILIO_PHONE_NUMBER=+**********
   ```

### **Option 2: AWS SNS**

1. **Set up AWS SNS** in AWS Console
2. **Set Environment Variables**:
   ```bash
   SMS_PROVIDER=sns
   AWS_ACCESS_KEY_ID=your-aws-access-key
   AWS_SECRET_ACCESS_KEY=your-aws-secret-key
   AWS_REGION=us-east-1
   ```

## 🚀 **Quick Setup for Testing**

### **Gmail SMTP (Recommended for Testing)**

1. **Create App Password**:
   - Gmail → Settings → Security → 2-Step Verification → App passwords
   - Select "Mail" and generate password

2. **Set Environment Variables** (Windows):
   ```cmd
   set EMAIL_PROVIDER=smtp
   set SMTP_USERNAME=<EMAIL>
   set SMTP_PASSWORD=your-16-char-app-password
   ```

3. **Test Email**:
   - Edit your Google Sheet with your real email
   - Create calendar event from sheet entry
   - Send notifications

## 🔧 **Installation Requirements**

### **For Email Providers**:
```bash
# No additional packages needed for SMTP
# For SendGrid:
pip install sendgrid

# For AWS SES:
pip install boto3
```

### **For SMS Providers**:
```bash
# For Twilio:
pip install twilio

# For AWS SNS:
pip install boto3
```

## 📋 **Environment Variables Summary**

Create a `.env` file or set these environment variables:

```bash
# Email Configuration
EMAIL_PROVIDER=smtp                    # smtp, sendgrid, or ses
SMTP_SERVER=smtp.gmail.com            # For SMTP
SMTP_PORT=587                         # For SMTP
SMTP_USERNAME=<EMAIL>    # Your email
SMTP_PASSWORD=your-app-password       # Gmail app password

# SMS Configuration (Optional)
SMS_PROVIDER=twilio                   # twilio or sns
TWILIO_ACCOUNT_SID=your-sid          # For Twilio
TWILIO_AUTH_TOKEN=your-token         # For Twilio
TWILIO_PHONE_NUMBER=+**********      # For Twilio

# Google Services (Already configured)
GOOGLE_CREDENTIALS_FILE=path/to/credentials.json
GOOGLE_SHEET_ID=your-sheet-id
```

## 🧪 **Testing Process**

1. **Set up email provider** (Gmail SMTP recommended)
2. **Edit Google Sheet** with your real email address
3. **Create calendar event** from sheet entry:
   ```bash
   POST /api/calendar/create_event_from_pii/
   {"sheet_entry_id": 12}
   ```
4. **Send notifications**:
   ```bash
   POST /api/calendar/{event_id}/send_notifications/
   ```
5. **Check your email** for the appointment confirmation

## 🔍 **Debugging**

### **Check Console Output**:
The system now shows detailed logging:
```
📧 Attempting to send <NAME_EMAIL>
   Subject: Appointment Confirmation
   Provider: smtp
✅ SMTP email sent <NAME_EMAIL>
```

### **Common Issues**:

1. **Gmail "Less secure app access"**:
   - Use App Password instead of regular password
   - Enable 2-Factor Authentication first

2. **SendGrid "Sender not verified"**:
   - Verify your sender email in SendGrid dashboard

3. **AWS "Access denied"**:
   - Check IAM permissions for SES/SNS
   - Verify AWS credentials

## 💡 **Fallback Behavior**

If no email/SMS provider is configured, the system will:
- ✅ Still create calendar events
- ✅ Log notification attempts to console
- ✅ Mark notifications as "sent" in database
- ⚠️  But won't send actual emails/SMS

## 🎯 **Recommended Setup for You**

**For immediate testing**:
1. Use **Gmail SMTP** (easiest setup)
2. Set your Gmail credentials in environment variables
3. Edit Google Sheet with your email
4. Test notifications

**For production**:
1. Use **SendGrid** for email (reliable, good free tier)
2. Use **Twilio** for SMS (industry standard)
3. Set up proper domain verification

## 📞 **Support**

If you need help setting up any of these services:
1. **Gmail SMTP**: Most straightforward, good for testing
2. **SendGrid**: Best for production email
3. **Twilio**: Best for production SMS

The system will now send **REAL** notifications instead of fake ones! 🎉
