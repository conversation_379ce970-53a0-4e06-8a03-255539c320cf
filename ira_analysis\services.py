import os
from django.conf import settings
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from .models import IRAReport
from audio_processing.models import Transcript
import json
from datetime import datetime

class GroqClient:
    """Client for interacting with Groq API"""

    def __init__(self, api_key=None):
        self.api_key = api_key or os.getenv('GROQ_API_KEY')
        if not self.api_key:
            raise ValueError("GROQ_API_KEY is required")

        # Updated to use current Groq model (mixtral-8x7b-32768 was decommissioned)
        self.model_name = "llama3-8b-8192"  # Alternative: "llama3-70b-8192" for better quality

        # Simple, direct initialization using the working pattern
        from groq import Groq
        self.client = Groq(api_key=self.api_key)

    def get_llm_response(self, prompt):
        """Get response from Groq LLM"""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            raise Exception(f"Error getting LLM response: {str(e)}")

class TokenizerUtils:
    """Utilities for token counting and text splitting"""

    def __init__(self):
        self.token_limit = 1024

    def count_tokens(self, text):
        """Simple token counting based on word count (approximation)"""
        return len(text.split())

    def sliding_window_split(self, transcript):
        """Sliding window approach to prevent information loss"""
        window_size = int(len(transcript) * 0.75)  # 75% overlap
        customer_reason_text = transcript[:window_size]  # First 75% for reason
        agent_action_text = transcript[-window_size:]  # Last 75% for action
        return customer_reason_text, agent_action_text

class IRAAnalyzer:
    """Interaction Report Analysis using Groq LLM"""
    
    def __init__(self, groq_api_key=None):
        self.groq_client = GroqClient(groq_api_key)
        self.tokenizer_utils = TokenizerUtils()
        
        # Define analysis prompts - optimized and concise
        self.prompts = {
            "customer_reason": """
Extract the **specific reason** the customer contacted support from the transcript.
Focus on the issue, request, or concern. Provide a concise summary in one sentence.
Transcript:
"{transcript}"
            """,
            "agent_action": """
Identify the **actions or solutions** the agent provided or should provide based on the transcript.
Summarize the agent's response or the required action in one sentence.
Transcript:
"{transcript}"
            """,
            "customer_intent": """
Determine the **primary intent** behind the customer's communication.
Summarize their main goal in reaching out in one sentence.
Transcript:
"{transcript}"
            """,
            "sentiment_analysis": """
Analyze the overall sentiment of this customer service interaction.
Rate the sentiment on a scale of -1 (very negative) to 1 (very positive).
Also provide a brief explanation of the sentiment.
Transcript:
"{transcript}"
            """,
            "call_summary": """
Provide a comprehensive summary of this customer service call including:
1. Main issue discussed
2. Resolution provided or attempted
3. Customer satisfaction level
4. Any follow-up actions needed

Transcript:
"{transcript}"
            """,
            "quality_assessment": """
Assess the quality of this customer service interaction based on:
1. Agent professionalism and helpfulness
2. Issue resolution effectiveness
3. Communication clarity
4. Overall customer experience

Provide a score from 1-10 and detailed feedback.
Transcript:
"{transcript}"
            """
        }
    
    def process_transcript(self, transcript_text):
        """Process transcript using optimized token management with sliding window"""
        token_count = self.tokenizer_utils.count_tokens(transcript_text)

        # Use sliding window if the token count exceeds the limit
        if token_count > self.tokenizer_utils.token_limit:
            customer_reason_text, agent_action_text = self.tokenizer_utils.sliding_window_split(transcript_text)
        else:
            # If within token limit, use the full transcript
            customer_reason_text = agent_action_text = transcript_text

        # Get LLM responses for all analysis types
        responses = {}

        try:
            print(f"🔍 Processing transcript with {token_count} tokens")

            # Process core analysis types first (matching reference code)
            try:
                print(f"🧠 Processing customer_reason...")
                responses["customer_reason"] = self.groq_client.get_llm_response(
                    self.prompts["customer_reason"].format(transcript=customer_reason_text)
                )
                print(f"✅ customer_reason completed")
            except Exception as e:
                print(f"❌ customer_reason failed: {str(e)}")
                responses["customer_reason"] = f"Error: {str(e)}"

            try:
                print(f"🧠 Processing agent_action...")
                responses["agent_action"] = self.groq_client.get_llm_response(
                    self.prompts["agent_action"].format(transcript=agent_action_text)
                )
                print(f"✅ agent_action completed")
            except Exception as e:
                print(f"❌ agent_action failed: {str(e)}")
                responses["agent_action"] = f"Error: {str(e)}"

            try:
                print(f"🧠 Processing customer_intent...")
                responses["customer_intent"] = self.groq_client.get_llm_response(
                    self.prompts["customer_intent"].format(transcript=transcript_text)
                )
                print(f"✅ customer_intent completed")
            except Exception as e:
                print(f"❌ customer_intent failed: {str(e)}")
                responses["customer_intent"] = f"Error: {str(e)}"

            # Process additional analysis types
            additional_types = ["sentiment_analysis", "call_summary", "quality_assessment"]
            for analysis_type in additional_types:
                try:
                    print(f"🧠 Processing {analysis_type}...")
                    responses[analysis_type] = self.groq_client.get_llm_response(
                        self.prompts[analysis_type].format(transcript=transcript_text)
                    )
                    print(f"✅ {analysis_type} completed")
                except Exception as e:
                    print(f"❌ {analysis_type} failed: {str(e)}")
                    responses[analysis_type] = f"Error: {str(e)}"

            responses["tokens_processed"] = token_count

        except Exception as e:
            print(f"❌ Overall processing failed: {str(e)}")
            responses["error"] = f"Error processing transcript: {str(e)}"

        return responses
    
    def extract_sentiment_score(self, sentiment_text):
        """Extract numerical sentiment score from sentiment analysis text"""
        try:
            # Look for numerical score in the sentiment analysis
            import re
            score_match = re.search(r'[-+]?[0-9]*\.?[0-9]+', sentiment_text)
            if score_match:
                score = float(score_match.group())
                # Ensure score is between -1 and 1
                return max(-1.0, min(1.0, score))
            return 0.0
        except:
            return 0.0
    
    def generate_ira_report(self, transcript_id):
        """Generate comprehensive IRA report for a transcript"""
        try:
            transcript = Transcript.objects.get(id=transcript_id)
            
            # Process the transcript
            analysis_results = self.process_transcript(transcript.text)
            
            # Extract sentiment score
            sentiment_score = self.extract_sentiment_score(
                analysis_results.get("sentiment_analysis", "")
            )
            
            # Create comprehensive report text
            report_text = self._format_report(analysis_results)
            
            # Create summary
            summary = analysis_results.get("call_summary", "No summary available")
            
            # Create or update IRA report
            ira_report, created = IRAReport.objects.get_or_create(
                transcript=transcript,
                defaults={
                    'report_text': report_text,
                    'summary': summary,
                    'sentiment_score': sentiment_score
                }
            )
            
            if not created:
                # Update existing report
                ira_report.report_text = report_text
                ira_report.summary = summary
                ira_report.sentiment_score = sentiment_score
                ira_report.save()
            
            # Generate and save report file
            report_file_content = self._generate_report_file(analysis_results, transcript)
            file_name = f"ira_report_{transcript.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            
            # Save file
            file_content = ContentFile(report_file_content.encode('utf-8'))
            file_path = default_storage.save(f"reports/{file_name}", file_content)
            ira_report.report_file = file_path
            ira_report.save()
            
            return ira_report
            
        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error generating IRA report: {str(e)}")
    
    def _format_report(self, analysis_results):
        """Format analysis results into a comprehensive report"""
        report_sections = []
        
        if "customer_reason" in analysis_results:
            report_sections.append(f"**Customer Reason:**\n{analysis_results['customer_reason']}\n")
        
        if "customer_intent" in analysis_results:
            report_sections.append(f"**Customer Intent:**\n{analysis_results['customer_intent']}\n")
        
        if "agent_action" in analysis_results:
            report_sections.append(f"**Agent Actions:**\n{analysis_results['agent_action']}\n")
        
        if "sentiment_analysis" in analysis_results:
            report_sections.append(f"**Sentiment Analysis:**\n{analysis_results['sentiment_analysis']}\n")
        
        if "quality_assessment" in analysis_results:
            report_sections.append(f"**Quality Assessment:**\n{analysis_results['quality_assessment']}\n")
        
        if "call_summary" in analysis_results:
            report_sections.append(f"**Call Summary:**\n{analysis_results['call_summary']}\n")
        
        if "tokens_processed" in analysis_results:
            report_sections.append(f"**Tokens Processed:** {analysis_results['tokens_processed']}")
        
        return "\n".join(report_sections)
    
    def _generate_report_file(self, analysis_results, transcript):
        """Generate a formatted report file"""
        report_content = f"""
IRA (Interaction Report Analysis)
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Transcript ID: {transcript.id}
Audio File: {transcript.audio.file.name if transcript.audio else 'N/A'}

{'='*60}
ANALYSIS RESULTS
{'='*60}

{self._format_report(analysis_results)}

{'='*60}
ORIGINAL TRANSCRIPT
{'='*60}

{transcript.text}

{'='*60}
END OF REPORT
{'='*60}
        """
        
        return report_content.strip()

