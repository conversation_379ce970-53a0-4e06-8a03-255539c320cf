from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from django.shortcuts import get_object_or_404
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import os

from .serializers import (
    AudioFileSerializer, TranscriptSerializer, PIIDataSerializer,
    IRAReportSerializer, CalendarEventSerializer
)
from audio_processing.models import AudioFile, Transcript
from audio_processing.services import AudioProcessor
from audio_processing.enhanced_services import TranscriptProcessor
from pii_extraction.models import PIIData
from pii_extraction.services import P<PERSON>Extractor, TranscriptCleaner
from pii_extraction.enhanced_services import <PERSON>hanced<PERSON>IExtractor, EnhancedTranscriptCleaner, PIIValidator
from ira_analysis.models import IRAReport
from ira_analysis.services import IRAAnalyzer
from calendar_scheduler.models import CalendarEvent
from calendar_scheduler.services import CalendarSchedulerService

class AudioProcessingViewSet(viewsets.ModelViewSet):
    queryset = AudioFile.objects.all()
    serializer_class = AudioFileSerializer
    parser_classes = (MultiPartParser, FormParser)
    
    def create(self, request, *args, **kwargs):
        """Upload audio file"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        audio_file = serializer.save()
        
        return Response({
            'id': audio_file.id,
            'message': 'Audio file uploaded successfully',
            'file_url': audio_file.file.url
        }, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    def process(self, request, pk=None):
        """Process audio file to generate transcript with speaker diarization"""
        audio_file = self.get_object()
        
        if audio_file.processed:
            return Response({
                'message': 'Audio file already processed',
                'transcript_id': audio_file.transcripts.first().id if audio_file.transcripts.exists() else None
            })
        
        try:
            # Initialize audio processor
            processor = AudioProcessor()
            
            # Process the audio file
            transcript = processor.process_audio_file(audio_file)
            
            return Response({
                'message': 'Audio processed successfully',
                'transcript_id': transcript.id,
                'transcript_text': transcript.text[:200] + '...' if len(transcript.text) > 200 else transcript.text,
                'dialogues_count': transcript.dialogues.count()
            })
            
        except Exception as e:
            return Response({
                'error': f'Error processing audio: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def process_with_diarization(self, request, pk=None):
        """Process audio with enhanced speaker diarization"""
        audio_file = self.get_object()
        
        try:
            # Get HuggingFace token from request or environment
            hf_token = request.data.get('hf_token') or os.getenv('HUGGINGFACE_TOKEN')
            
            # Initialize enhanced processor
            processor = TranscriptProcessor(hf_token)
            
            # First, do basic transcription
            basic_processor = AudioProcessor()
            transcript = basic_processor.process_audio_file(audio_file)
            
            # Then enhance with speaker diarization
            # This would require the actual audio file path and transcript segments
            # For now, we'll return the basic transcript
            
            return Response({
                'message': 'Audio processed with enhanced diarization',
                'transcript_id': transcript.id,
                'transcript_text': transcript.text[:200] + '...' if len(transcript.text) > 200 else transcript.text,
                'dialogues_count': transcript.dialogues.count()
            })
            
        except Exception as e:
            return Response({
                'error': f'Error processing audio with diarization: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def get_transcript(self, request, pk=None):
        """Get transcript for an audio file"""
        audio_file = self.get_object()
        transcripts = audio_file.transcripts.all()
        serializer = TranscriptSerializer(transcripts, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def clean_transcript(self, request, pk=None):
        """Clean transcript text"""
        audio_file = self.get_object()
        
        try:
            transcript = audio_file.transcripts.first()
            if not transcript:
                return Response({
                    'error': 'No transcript found for this audio file'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Initialize cleaner
            cleaner = EnhancedTranscriptCleaner()
            
            # Clean the transcript
            cleaned_transcript = cleaner.clean_transcript_dialogues(transcript.id)
            
            return Response({
                'message': 'Transcript cleaned successfully',
                'transcript_id': cleaned_transcript.id,
                'cleaned_text': cleaned_transcript.text[:200] + '...' if len(cleaned_transcript.text) > 200 else cleaned_transcript.text
            })
            
        except Exception as e:
            return Response({
                'error': f'Error cleaning transcript: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PIIExtractionViewSet(viewsets.ModelViewSet):
    queryset = PIIData.objects.all()
    serializer_class = PIIDataSerializer
    
    @action(detail=False, methods=['post'])
    def extract_from_transcript(self, request):
        """Extract PII from a transcript"""
        transcript_id = request.data.get('transcript_id')
        if not transcript_id:
            return Response({
                "error": "transcript_id is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Initialize enhanced PII extractor
            extractor = EnhancedPIIExtractor()
            
            # Extract PII from transcript
            pii_data = extractor.extract_pii_from_transcript(transcript_id)
            
            # Validate PII data
            validator = PIIValidator()
            validation_results = validator.validate_pii_data({
                'names': pii_data.names,
                'phone_numbers': pii_data.phone_numbers,
                'emails': pii_data.emails,
                'addresses': pii_data.addresses,
                'appointment_dates': pii_data.appointment_dates,
                'appointment_times': pii_data.appointment_times
            })
            
            # Update confidence scores
            pii_data.confidence_scores = validation_results
            pii_data.save()
            
            # Serialize and return the data
            serializer = self.get_serializer(pii_data)
            
            return Response({
                'message': 'PII extracted successfully',
                'pii_data': serializer.data,
                'validation_results': validation_results
            })
            
        except Exception as e:
            return Response({
                'error': f'Error extracting PII: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def save_to_sheets(self, request, pk=None):
        """Save PII data to Google Sheets"""
        pii_data = self.get_object()
        
        try:
            # Initialize Google Sheets service
            credentials_file = request.data.get('credentials_file') or os.getenv('GOOGLE_CREDENTIALS_FILE')
            sheet_id = request.data.get('sheet_id') or os.getenv('GOOGLE_SHEET_ID')
            
            if not credentials_file:
                return Response({
                    'error': 'Google credentials file is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            from calendar_scheduler.services import GoogleSheetsService
            sheets_service = GoogleSheetsService(credentials_file, sheet_id)
            
            # Save to sheets
            entry = sheets_service.save_pii_to_sheets(pii_data)
            
            return Response({
                'message': 'PII data saved to Google Sheets successfully',
                'sheet_entry_id': entry.id,
                'row_number': entry.row_number
            })
            
        except Exception as e:
            return Response({
                'error': f'Error saving to Google Sheets: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def validate(self, request, pk=None):
        """Validate PII data and return confidence scores"""
        pii_data = self.get_object()
        
        try:
            validator = PIIValidator()
            validation_results = validator.validate_pii_data({
                'names': pii_data.names,
                'phone_numbers': pii_data.phone_numbers,
                'emails': pii_data.emails,
                'addresses': pii_data.addresses,
                'appointment_dates': pii_data.appointment_dates,
                'appointment_times': pii_data.appointment_times
            })
            
            return Response({
                'validation_results': validation_results
            })
            
        except Exception as e:
            return Response({
                'error': f'Error validating PII: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class IRAAnalysisViewSet(viewsets.ModelViewSet):
    queryset = IRAReport.objects.all()
    serializer_class = IRAReportSerializer
    
    @action(detail=False, methods=['post'])
    def analyze_transcript(self, request):
        """Generate IRA report from transcript"""
        transcript_id = request.data.get('transcript_id')
        groq_api_key = request.data.get('groq_api_key') or os.getenv('GROQ_API_KEY')
        
        if not transcript_id:
            return Response({
                "error": "transcript_id is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not groq_api_key:
            return Response({
                "error": "groq_api_key is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Initialize IRA analyzer
            analyzer = IRAAnalyzer(groq_api_key)
            
            # Generate IRA report
            ira_report = analyzer.generate_ira_report(transcript_id)
            
            # Serialize and return the data
            serializer = self.get_serializer(ira_report)
            
            return Response({
                'message': 'IRA report generated successfully',
                'report': serializer.data
            })
            
        except Exception as e:
            return Response({
                'error': f'Error generating IRA report: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['get'])
    def download_report(self, request, pk=None):
        """Download IRA report file"""
        ira_report = self.get_object()
        
        if not ira_report.report_file:
            return Response({
                'error': 'No report file available'
            }, status=status.HTTP_404_NOT_FOUND)
        
        try:
            # Return file URL for download
            return Response({
                'download_url': ira_report.report_file.url,
                'file_name': os.path.basename(ira_report.report_file.name)
            })
            
        except Exception as e:
            return Response({
                'error': f'Error accessing report file: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CalendarSchedulerViewSet(viewsets.ModelViewSet):
    queryset = CalendarEvent.objects.all()
    serializer_class = CalendarEventSerializer
    
    @action(detail=False, methods=['post'])
    def create_event_from_pii(self, request):
        """Create calendar event from PII data"""
        pii_data_id = request.data.get('pii_data_id')
        credentials_file = request.data.get('credentials_file') or os.getenv('GOOGLE_CREDENTIALS_FILE')
        
        if not pii_data_id:
            return Response({
                "error": "pii_data_id is required"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not credentials_file:
            return Response({
                'error': 'Google credentials file is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Initialize calendar scheduler service
            scheduler_service = CalendarSchedulerService(credentials_file)
            
            # Create calendar event
            calendar_event = scheduler_service.create_event_from_pii(pii_data_id)
            
            # Serialize and return the data
            serializer = self.get_serializer(calendar_event)
            
            return Response({
                'message': 'Calendar event created successfully',
                'event': serializer.data
            })
            
        except Exception as e:
            return Response({
                'error': f'Error creating calendar event: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def send_notifications(self, request, pk=None):
        """Send email and SMS notifications for an event"""
        calendar_event = self.get_object()
        credentials_file = request.data.get('credentials_file') or os.getenv('GOOGLE_CREDENTIALS_FILE')
        
        try:
            # Initialize calendar scheduler service
            scheduler_service = CalendarSchedulerService(credentials_file)
            
            # Send notifications
            notification_results = scheduler_service.send_notifications(calendar_event.id)
            
            return Response({
                'message': 'Notifications sent successfully',
                'results': notification_results
            })
            
        except Exception as e:
            return Response({
                'error': f'Error sending notifications: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=False, methods=['post'])
    def process_sheet_entries(self, request):
        """Process new entries from Google Sheets"""
        credentials_file = request.data.get('credentials_file') or os.getenv('GOOGLE_CREDENTIALS_FILE')
        sheet_id = request.data.get('sheet_id') or os.getenv('GOOGLE_SHEET_ID')
        
        if not credentials_file:
            return Response({
                'error': 'Google credentials file is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Initialize calendar scheduler service
            scheduler_service = CalendarSchedulerService(credentials_file, sheet_id)
            
            # Process new sheet entries
            processed_events = scheduler_service.process_new_sheet_entries()
            
            return Response({
                'message': f'Processed {len(processed_events)} new entries',
                'processed_events': processed_events
            })
            
        except Exception as e:
            return Response({
                'error': f'Error processing sheet entries: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Additional utility endpoints
class UtilityViewSet(viewsets.ViewSet):
    """Utility endpoints for the integrated platform"""
    
    @action(detail=False, methods=['post'])
    def full_workflow(self, request):
        """Execute the complete workflow: Audio -> Transcript -> PII -> IRA -> Calendar"""
        audio_file_id = request.data.get('audio_file_id')
        groq_api_key = request.data.get('groq_api_key') or os.getenv('GROQ_API_KEY')
        credentials_file = request.data.get('credentials_file') or os.getenv('GOOGLE_CREDENTIALS_FILE')
        
        if not audio_file_id:
            return Response({
                'error': 'audio_file_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        workflow_results = {}
        
        try:
            # Step 1: Process audio
            audio_file = get_object_or_404(AudioFile, id=audio_file_id)
            processor = AudioProcessor()
            transcript = processor.process_audio_file(audio_file)
            workflow_results['transcript_id'] = transcript.id
            
            # Step 2: Clean transcript
            cleaner = EnhancedTranscriptCleaner()
            cleaned_transcript = cleaner.clean_transcript_dialogues(transcript.id)
            workflow_results['transcript_cleaned'] = True
            
            # Step 3: Extract PII
            extractor = EnhancedPIIExtractor()
            pii_data = extractor.extract_pii_from_transcript(transcript.id)
            workflow_results['pii_data_id'] = pii_data.id
            
            # Step 4: Generate IRA report (if Groq API key provided)
            if groq_api_key:
                analyzer = IRAAnalyzer(groq_api_key)
                ira_report = analyzer.generate_ira_report(transcript.id)
                workflow_results['ira_report_id'] = ira_report.id
            
            # Step 5: Save to Google Sheets (if credentials provided)
            if credentials_file:
                from calendar_scheduler.services import GoogleSheetsService
                sheets_service = GoogleSheetsService(credentials_file)
                entry = sheets_service.save_pii_to_sheets(pii_data)
                workflow_results['sheets_entry_id'] = entry.id
            
            # Step 6: Create calendar event (if credentials and sufficient PII data)
            if credentials_file and pii_data.names != ["Not Found"] and pii_data.emails != ["Not Found"]:
                try:
                    scheduler_service = CalendarSchedulerService(credentials_file)
                    calendar_event = scheduler_service.create_event_from_pii(pii_data.id)
                    workflow_results['calendar_event_id'] = calendar_event.id
                except Exception as e:
                    workflow_results['calendar_error'] = str(e)
            
            return Response({
                'message': 'Full workflow completed successfully',
                'results': workflow_results
            })
            
        except Exception as e:
            return Response({
                'error': f'Error in workflow: {str(e)}',
                'partial_results': workflow_results
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

