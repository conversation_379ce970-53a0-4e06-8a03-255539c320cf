import spacy
import re
from django.conf import settings
from .models import PIIData
from audio_processing.models import Transcript

class PIIExtractor:
    def __init__(self):
        """Initialize PII extractor with spaCy model"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            # If model not found, we'll handle this gracefully
            self.nlp = None
        
        # Define regex patterns for PII extraction
        self.patterns = {
            'phone': r'\b(?:\+?1[-.]?)?\s*\(?([0-9]{3})\)?[-.\s]*([0-9]{3})[-.\s]*([0-9]{4})\b',
            'email': r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            'date': [
                r'\b(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|'
                r'Jul(?:y)?|Aug(?:ust)?|Sep(?:tember)?|Oct(?:ober)?|Nov(?:ember)?|'
                r'Dec(?:ember)?)\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4}\b',
                r'\b\d{1,2}[-/]\d{1,2}[-/]\d{2,4}\b'
            ],
            'time': r'\b(?:1[0-2]|0?[1-9])(?:[:][0-5][0-9])?\s*(?:am|pm|AM|PM)\b',
        }
    
    def extract_names(self, text):
        """Extract person names from text using spaCy NER"""
        if not self.nlp:
            return ["Not Found"]
        
        doc = self.nlp(text)
        names = [ent.text for ent in doc.ents if ent.label_ == "PERSON"]
        return names if names else ["Not Found"]
    
    def extract_addresses(self, text):
        """Extract addresses from text using spaCy NER"""
        if not self.nlp:
            return ["Not Found"]
        
        doc = self.nlp(text)
        addresses = [ent.text for ent in doc.ents if ent.label_ in ["GPE", "LOC", "FAC"]]
        return addresses if addresses else ["Not Found"]
    
    def extract_phone_numbers(self, text):
        """Extract phone numbers using regex"""
        matches = re.finditer(self.patterns['phone'], text)
        phones = [f"{m.group(1)}-{m.group(2)}-{m.group(3)}" for m in matches]
        return phones if phones else ["Not Found"]
    
    def extract_emails(self, text):
        """Extract email addresses using regex"""
        emails = re.findall(self.patterns['email'], text)
        return emails if emails else ["Not Found"]
    
    def extract_dates(self, text):
        """Extract dates using regex patterns"""
        dates = []
        for pattern in self.patterns['date']:
            dates.extend(re.findall(pattern, text))
        return dates if dates else ["Not Found"]
    
    def extract_times(self, text):
        """Extract times using regex"""
        times = re.findall(self.patterns['time'], text)
        return times if times else ["Not Found"]
    
    def extract_pii_from_transcript(self, transcript_id):
        """
        Extract all PII from a transcript and save to database
        """
        try:
            transcript = Transcript.objects.get(id=transcript_id)
            text = transcript.text
            
            # Extract all PII types
            extracted_data = {
                "names": self.extract_names(text),
                "phone_numbers": self.extract_phone_numbers(text),
                "emails": self.extract_emails(text),
                "addresses": self.extract_addresses(text),
                "appointment_dates": self.extract_dates(text),
                "appointment_times": self.extract_times(text)
            }
            
            # Create or update PII data record
            pii_data, created = PIIData.objects.get_or_create(
                transcript=transcript,
                defaults=extracted_data
            )
            
            if not created:
                # Update existing record
                for key, value in extracted_data.items():
                    setattr(pii_data, key, value)
                pii_data.save()
            
            return pii_data
            
        except Transcript.DoesNotExist:
            raise Exception(f"Transcript with id {transcript_id} not found")
        except Exception as e:
            raise Exception(f"Error extracting PII: {str(e)}")

class TranscriptCleaner:
    """Clean and process transcripts"""
    
    def __init__(self):
        self.filler_words = [
            'um', 'uh', 'er', 'ah', 'like', 'you know', 'sort of', 'kind of'
        ]
    
    def clean_transcript(self, text):
        """
        Clean transcript by removing filler words, fixing punctuation, etc.
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove filler words (case insensitive)
        for filler in self.filler_words:
            pattern = r'\b' + re.escape(filler) + r'\b'
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # Fix multiple spaces
        text = re.sub(r'\s+', ' ', text)
        
        # Capitalize first letter of sentences
        text = re.sub(r'(^|[.!?]\s+)([a-z])', lambda m: m.group(1) + m.group(2).upper(), text)
        
        return text.strip()
    
    def process_dialogue_text(self, dialogue_text):
        """Process individual dialogue text"""
        return self.clean_transcript(dialogue_text)

