#!/usr/bin/env python3
"""
Debug notification system to show why emails/SMS aren't being sent
"""

import requests
import json
import os

BASE_URL = "http://127.0.0.1:8000/api"

def check_environment_variables():
    """Check what email/SMS providers are configured"""
    print("🔧 CHECKING ENVIRONMENT CONFIGURATION")
    print("=" * 50)
    
    # Email configuration
    email_provider = os.getenv('EMAIL_PROVIDER', 'not set')
    smtp_username = os.getenv('SMTP_USERNAME', 'not set')
    smtp_password = os.getenv('SMTP_PASSWORD', 'not set')
    sendgrid_key = os.getenv('SENDGRID_API_KEY', 'not set')
    
    print(f"📧 Email Configuration:")
    print(f"   EMAIL_PROVIDER: {email_provider}")
    print(f"   SMTP_USERNAME: {smtp_username}")
    print(f"   SMTP_PASSWORD: {'*' * len(smtp_password) if smtp_password != 'not set' else 'not set'}")
    print(f"   SENDGRID_API_KEY: {'*' * 10 if sendgrid_key != 'not set' else 'not set'}")
    
    # SMS configuration
    sms_provider = os.getenv('SMS_PROVIDER', 'not set')
    twilio_sid = os.getenv('TWILIO_ACCOUNT_SID', 'not set')
    twilio_token = os.getenv('TWILIO_AUTH_TOKEN', 'not set')
    twilio_phone = os.getenv('TWILIO_PHONE_NUMBER', 'not set')
    
    print(f"\n📱 SMS Configuration:")
    print(f"   SMS_PROVIDER: {sms_provider}")
    print(f"   TWILIO_ACCOUNT_SID: {'*' * 10 if twilio_sid != 'not set' else 'not set'}")
    print(f"   TWILIO_AUTH_TOKEN: {'*' * 10 if twilio_token != 'not set' else 'not set'}")
    print(f"   TWILIO_PHONE_NUMBER: {twilio_phone}")
    
    # Determine what will happen
    print(f"\n🔍 What Will Happen:")
    if email_provider == 'not set' or smtp_username == 'not set':
        print(f"   📧 Email: MOCK/FAKE (will only print to console)")
        print(f"      Reason: No email provider configured")
    else:
        print(f"   📧 Email: REAL (will attempt to send via {email_provider})")
    
    if sms_provider == 'not set' or (twilio_sid == 'not set' and sms_provider == 'twilio'):
        print(f"   📱 SMS: MOCK/FAKE (will only print to console)")
        print(f"      Reason: No SMS provider configured")
    else:
        print(f"   📱 SMS: REAL (will attempt to send via {sms_provider})")

def test_notification_sending():
    """Test sending notifications and show what happens"""
    print(f"\n📬 TESTING NOTIFICATION SENDING")
    print("=" * 50)
    
    try:
        # First, create a calendar event to get an event ID
        print("📅 Step 1: Creating calendar event...")
        
        # Get latest PII and save to sheets
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    # Create calendar event
                    response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                                           json={"pii_data_id": pii_id},
                                           headers={'Content-Type': 'application/json'})
                    
                    if response.status_code == 200:
                        event_data = response.json()
                        event_details = event_data.get('event', {})
                        event_id = event_details.get('id')
                        
                        print(f"✅ Calendar event created - ID: {event_id}")
                        print(f"   Email: {event_details.get('email')}")
                        print(f"   Phone: {event_details.get('phone')}")
                        
                        # Now test sending notifications
                        print(f"\n📬 Step 2: Sending notifications...")
                        response = requests.post(f"{BASE_URL}/calendar/{event_id}/send_notifications/")
                        
                        if response.status_code == 200:
                            notification_data = response.json()
                            print(f"✅ Notification API call successful!")
                            print(f"   Response: {json.dumps(notification_data, indent=2)}")
                            
                            # Explain what happened
                            print(f"\n🔍 What Actually Happened:")
                            print(f"   📧 Email Status: {notification_data.get('results', {}).get('email_sent', 'unknown')}")
                            print(f"   📱 SMS Status: {notification_data.get('results', {}).get('sms_sent', 'unknown')}")
                            
                            # Check environment to explain why
                            email_provider = os.getenv('EMAIL_PROVIDER', 'not set')
                            if email_provider == 'not set':
                                print(f"   📧 Email: Showed 'sent' but was FAKE (no provider configured)")
                                print(f"      To fix: Set EMAIL_PROVIDER, SMTP_USERNAME, SMTP_PASSWORD")
                            else:
                                print(f"   📧 Email: Attempted real sending via {email_provider}")
                            
                            sms_provider = os.getenv('SMS_PROVIDER', 'not set')
                            if sms_provider == 'not set':
                                print(f"   📱 SMS: Showed 'sent' but was FAKE (no provider configured)")
                                print(f"      To fix: Set SMS_PROVIDER, TWILIO credentials")
                            else:
                                print(f"   📱 SMS: Attempted real sending via {sms_provider}")
                            
                        else:
                            print(f"❌ Notification sending failed: {response.status_code}")
                            try:
                                error_data = response.json()
                                print(f"   Error: {error_data.get('error', 'Unknown error')}")
                            except:
                                print(f"   Raw response: {response.text}")
                    else:
                        print(f"❌ Calendar event creation failed: {response.status_code}")
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def show_setup_instructions():
    """Show quick setup instructions"""
    print(f"\n📋 QUICK SETUP FOR REAL NOTIFICATIONS")
    print("=" * 50)
    
    print(f"🔧 To receive REAL emails (Gmail SMTP):")
    print(f"   1. Enable 2-Factor Authentication on Gmail")
    print(f"   2. Generate App Password: Gmail → Security → App passwords")
    print(f"   3. Set environment variables:")
    print(f"      set EMAIL_PROVIDER=smtp")
    print(f"      set SMTP_USERNAME=<EMAIL>")
    print(f"      set SMTP_PASSWORD=your-16-char-app-password")
    print(f"   4. Restart your Django server")
    print(f"   5. Edit Google Sheet with your real email")
    print(f"   6. Test notifications again")
    
    print(f"\n📱 To receive REAL SMS (Twilio):")
    print(f"   1. Sign up at https://twilio.com (free trial)")
    print(f"   2. Get phone number from Twilio console")
    print(f"   3. Set environment variables:")
    print(f"      set SMS_PROVIDER=twilio")
    print(f"      set TWILIO_ACCOUNT_SID=your-account-sid")
    print(f"      set TWILIO_AUTH_TOKEN=your-auth-token")
    print(f"      set TWILIO_PHONE_NUMBER=+**********")
    print(f"   4. Restart your Django server")
    print(f"   5. Test notifications again")

if __name__ == "__main__":
    print("🔍 NOTIFICATION SYSTEM DEBUG")
    print("=" * 60)
    print("Checking why you're not receiving emails/SMS")
    print()
    
    # Check environment configuration
    check_environment_variables()
    
    # Test notification sending
    test_notification_sending()
    
    # Show setup instructions
    show_setup_instructions()
    
    print("\n" + "=" * 60)
    print("✨ DEBUG COMPLETED")
    print("=" * 60)
    print("\n🎯 Summary:")
    print("• Your notifications are currently FAKE/MOCK")
    print("• They only print to console, not send real emails/SMS")
    print("• Follow the setup instructions above to enable REAL notifications")
    print("• The system will show 'sent' even for fake notifications")
