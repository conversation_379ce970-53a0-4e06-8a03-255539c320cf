# Updated Calendar API Documentation

## 🎯 **API Endpoint Updated**

**Endpoint**: `POST /api/calendar/create_event_from_pii/`

**Description**: Create calendar events from either PII data or Google Sheets entries

## 📊 **Request Options**

### **Option 1: Using PII Data ID (Original)**
```json
{
    "pii_data_id": 5,
    "credentials_file": "path/to/credentials.json"  // Optional if env var set
}
```

### **Option 2: Using Sheet Entry ID (New)**
```json
{
    "sheet_entry_id": 10,
    "credentials_file": "path/to/credentials.json"  // Optional if env var set
}
```

## ✅ **Requirements**

### **Minimum Required Data:**
- ✅ **Name** - At least one name must be present
- ✅ **Email** - At least one email must be present

### **Optional Data (with defaults):**
- 📅 **Date** - Defaults to today if missing
- ⏰ **Time** - Defaults to 10:00 AM if missing
- 📍 **Location** - Optional (empty if missing)
- 📞 **Phone** - Optional (empty if missing)

## 🔄 **Workflow Examples**

### **Method 1: Direct from PII Data**
```bash
# 1. Extract PII from transcript
POST /api/pii/extract_from_transcript/
{"transcript_id": 5}
# Returns: {"pii_data": {"id": 7, ...}}

# 2. Create calendar event directly
POST /api/calendar/create_event_from_pii/
{"pii_data_id": 7}
# Returns: Calendar event details
```

### **Method 2: Via Google Sheets (Recommended)**
```bash
# 1. Extract PII from transcript
POST /api/pii/extract_from_transcript/
{"transcript_id": 5}
# Returns: {"pii_data": {"id": 7, ...}}

# 2. Save to Google Sheets
POST /api/pii/7/save_to_sheets/
# Returns: {"sheet_entry_id": 10, "row_number": 15}

# 3. Create calendar event from sheet entry
POST /api/calendar/create_event_from_pii/
{"sheet_entry_id": 10}
# Returns: Calendar event details + marks sheet entry as processed
```

## 📋 **Response Format**

### **Success Response (200)**
```json
{
    "message": "Calendar event created successfully",
    "event": {
        "id": 123,
        "name": "Jane Doe",
        "email": "<EMAIL>",
        "date": "2025-12-05",
        "time": "14:30:00",
        "location": "123 Main Street",
        "phone": "************",
        "event_link": "https://calendar.google.com/calendar/event?eid=...",
        "calendar_id": "primary",
        "created_at": "2025-06-10T12:00:00Z"
    }
}
```

### **Error Responses**

#### **400 - Missing Required Data**
```json
{
    "error": "Either pii_data_id or sheet_entry_id is required"
}
```

#### **400 - Missing Credentials**
```json
{
    "error": "Google credentials file is required"
}
```

#### **500 - Insufficient PII Data**
```json
{
    "error": "Error creating calendar event: Insufficient data: Name and email are required for calendar event creation"
}
```

#### **500 - Invalid ID**
```json
{
    "error": "Error creating calendar event: Google Sheets entry with id 999 not found"
}
```

## 🎯 **Key Features**

### **✅ Flexible Input**
- Accept both `pii_data_id` and `sheet_entry_id`
- Only one ID required (not both)
- Clear error messages for missing data

### **✅ Smart Defaults**
- Missing dates → Use today's date
- Missing times → Use 10:00 AM
- Missing location/phone → Empty strings

### **✅ Data Validation**
- Ensures name and email are present
- Filters out "Not Found" and "None" values
- Parses various date/time formats

### **✅ Google Sheets Integration**
- Automatically marks sheet entries as processed
- Maintains data consistency between sheets and calendar
- Supports the correct column order (name, email, date, time, location, contact)

## 🔧 **Implementation Details**

### **Date/Time Parsing**
The API handles various date and time formats:

**Date Formats Supported:**
- `2025-12-05` (YYYY-MM-DD)
- `5th December` (text format)
- `December 5th` (text format)
- `05/12/2025` (DD/MM/YYYY)
- `12/05/2025` (MM/DD/YYYY)

**Time Formats Supported:**
- `14:30` (24-hour format)
- `2:30 PM` (12-hour format)
- `2:30PM` (12-hour format without space)
- `14.30` (dot separator)

### **Google Calendar Integration**
- Creates events in "Appointments" calendar (creates if doesn't exist)
- Removes attendees to avoid service account permission issues
- Sets 1-hour duration by default
- Includes location and description

## 🚀 **Usage Recommendations**

### **For Direct PII Processing:**
```bash
POST /api/calendar/create_event_from_pii/
{"pii_data_id": 5}
```
**Use when**: You want to create events immediately after PII extraction

### **For Google Sheets Workflow:**
```bash
POST /api/calendar/create_event_from_pii/
{"sheet_entry_id": 10}
```
**Use when**: 
- You want to track which entries have been processed
- You're managing data through Google Sheets
- You want better data organization and workflow

## 💡 **Best Practices**

1. **Always use sheet_entry_id method** for production workflows
2. **Ensure PII extraction finds names and emails** before creating events
3. **Set GOOGLE_CREDENTIALS_FILE environment variable** to avoid passing credentials in requests
4. **Check response for event_link** to get the Google Calendar URL
5. **Handle errors gracefully** - missing data is common in transcripts

## 🔍 **Troubleshooting**

### **"Name and email are required" Error**
- Check PII extraction results
- Ensure transcript contains clear customer information
- Verify names aren't being filtered as "Not Found"

### **"Google credentials file is required" Error**
- Set `GOOGLE_CREDENTIALS_FILE` environment variable
- Or pass `credentials_file` in request body

### **"Service accounts cannot invite attendees" Error**
- This has been fixed - attendees are no longer added to events
- Events are created without attendee invitations

### **Date/Time Parsing Issues**
- The API now handles various formats automatically
- Missing dates default to today
- Missing times default to 10:00 AM

## 📊 **Testing**

Use the provided test script:
```bash
python test_sheet_entry_calendar.py
```

This will test both methods and show you working examples with real data.
