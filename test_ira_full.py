#!/usr/bin/env python3
"""
Full test for IRA analysis API
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_ira_analysis_full():
    """Test IRA analysis with full output"""
    print("🧠 TESTING IRA ANALYSIS API - FULL OUTPUT")
    print("=" * 60)
    
    try:
        # Get the latest transcript
        print("📄 Getting latest transcript...")
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                transcript_text = data['transcripts'][0]['text']
                
                print(f"✅ Found transcript ID: {transcript_id}")
                print(f"📄 Transcript preview: {transcript_text[:200]}...")
                
                # Test IRA analysis
                print(f"\n🧠 Running IRA analysis...")
                response = requests.post(f"{BASE_URL}/ira/analyze_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ IRA analysis successful!")
                    
                    # Display all available data
                    print(f"\n📊 Analysis Results:")
                    for key, value in data.items():
                        if key == 'report_text' and value:
                            print(f"   {key}: {value[:300]}...")
                        elif key == 'summary' and value:
                            print(f"   {key}: {value[:200]}...")
                        else:
                            print(f"   {key}: {value}")
                    
                    # Check if report was saved
                    report_id = data.get('report_id')
                    if report_id:
                        print(f"\n📋 Report saved with ID: {report_id}")
                        
                        # Try to get the report details
                        try:
                            report_response = requests.get(f"{BASE_URL}/ira/reports/{report_id}/")
                            if report_response.status_code == 200:
                                report_data = report_response.json()
                                print(f"✅ Report details retrieved")
                                print(f"   Report file: {report_data.get('report_file', 'N/A')}")
                                print(f"   Created: {report_data.get('created_at', 'N/A')}")
                            else:
                                print(f"⚠️  Could not retrieve report details: {report_response.status_code}")
                        except Exception as e:
                            print(f"⚠️  Error retrieving report: {str(e)}")
                    else:
                        print(f"⚠️  No report ID returned")
                    
                else:
                    print(f"❌ IRA analysis failed: {response.status_code}")
                    try:
                        error_data = response.json()
                        print(f"   Error: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   Raw response: {response.text}")
            else:
                print("❌ No transcripts found")
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_ira_reports_list():
    """Test getting list of IRA reports"""
    print(f"\n📋 TESTING IRA REPORTS LIST")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/ira/reports/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Reports list retrieved")
            
            if isinstance(data, list):
                print(f"   Found {len(data)} reports")
                for i, report in enumerate(data[:3]):  # Show first 3 reports
                    print(f"   Report {i+1}:")
                    print(f"     ID: {report.get('id')}")
                    print(f"     Transcript ID: {report.get('transcript_id')}")
                    print(f"     Sentiment Score: {report.get('sentiment_score')}")
                    print(f"     Created: {report.get('created_at')}")
            else:
                print(f"   Response: {data}")
        else:
            print(f"❌ Error getting reports: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE IRA ANALYSIS TEST")
    print("=" * 60)
    print("Testing the complete IRA analysis functionality")
    print()
    
    # Test IRA analysis
    test_ira_analysis_full()
    
    # Test reports list
    test_ira_reports_list()
    
    print("\n" + "=" * 60)
    print("✨ COMPREHENSIVE TEST COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• IRA analysis should now work without 'proxies' error")
    print("• Groq package upgraded to version 0.26.0")
    print("• Analysis includes sentiment, summary, and quality assessment")
    print("• Reports are saved and can be retrieved")
    print("\n🎯 Your IRA analysis system is now fully functional!")
