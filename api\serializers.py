from rest_framework import serializers
from audio_processing.models import AudioFile, Transcript, Speaker, Dialogue
from pii_extraction.models import PIIData, GoogleSheetsEntry, PIIValidation
from ira_analysis.models import IRAReport
from calendar_scheduler.models import CalendarEvent, EmailNotification, SMSNotification

class DialogueSerializer(serializers.ModelSerializer):
    speaker_name = serializers.CharField(source='speaker.name', read_only=True)
    
    class Meta:
        model = Dialogue
        fields = ['id', 'speaker_name', 'text', 'start_time', 'end_time']

class SpeakerSerializer(serializers.ModelSerializer):
    dialogues = DialogueSerializer(many=True, read_only=True)
    
    class Meta:
        model = Speaker
        fields = ['id', 'name', 'dialogues']

class TranscriptSerializer(serializers.ModelSerializer):
    dialogues = DialogueSerializer(many=True, read_only=True)
    speakers = SpeakerSerializer(many=True, read_only=True)
    
    class Meta:
        model = Transcript
        fields = ['id', 'text', 'created_at', 'dialogues', 'speakers']

class AudioFileSerializer(serializers.ModelSerializer):
    transcripts = TranscriptSerializer(many=True, read_only=True)
    
    class Meta:
        model = AudioFile
        fields = ['id', 'file', 'uploaded_at', 'processed', 'transcripts']

class PIIValidationSerializer(serializers.ModelSerializer):
    class Meta:
        model = PIIValidation
        fields = ['id', 'validation_results', 'overall_confidence', 'validated_at']

class PIIDataSerializer(serializers.ModelSerializer):
    transcript_id = serializers.IntegerField(source='transcript.id', read_only=True)
    validation = PIIValidationSerializer(read_only=True)
    
    class Meta:
        model = PIIData
        fields = ['id', 'transcript_id', 'names', 'phone_numbers', 'emails', 
                  'addresses', 'appointment_dates', 'appointment_times', 'ssn', 
                  'credit_cards', 'confidence_scores', 'created_at', 'updated_at', 'validation']

class GoogleSheetsEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = GoogleSheetsEntry
        fields = ['id', 'pii_data', 'sheet_id', 'row_number', 'processed', 'created_at']

class IRAReportSerializer(serializers.ModelSerializer):
    transcript_id = serializers.IntegerField(source='transcript.id', read_only=True)
    
    class Meta:
        model = IRAReport
        fields = ['id', 'transcript_id', 'report_text', 'summary', 
                  'sentiment_score', 'customer_reason', 'agent_action', 
                  'customer_intent', 'quality_score', 'tokens_processed',
                  'created_at', 'updated_at', 'report_file']

class EmailNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailNotification
        fields = ['id', 'calendar_event', 'sent_at', 'status']

class SMSNotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSNotification
        fields = ['id', 'calendar_event', 'sent_at', 'status']

class CalendarEventSerializer(serializers.ModelSerializer):
    email_notifications = EmailNotificationSerializer(many=True, read_only=True)
    sms_notifications = SMSNotificationSerializer(many=True, read_only=True)
    
    class Meta:
        model = CalendarEvent
        fields = ['id', 'pii_data', 'calendar_id', 'event_id', 'name', 'email', 
                  'phone', 'date', 'time', 'location', 'event_link', 'created_at',
                  'email_notifications', 'sms_notifications']

# Workflow serializers for complex operations
class WorkflowRequestSerializer(serializers.Serializer):
    audio_file_id = serializers.IntegerField()
    groq_api_key = serializers.CharField(required=False)
    credentials_file = serializers.CharField(required=False)
    hf_token = serializers.CharField(required=False)
    sheet_id = serializers.CharField(required=False)

class WorkflowResponseSerializer(serializers.Serializer):
    message = serializers.CharField()
    transcript_id = serializers.IntegerField(required=False)
    pii_data_id = serializers.IntegerField(required=False)
    ira_report_id = serializers.IntegerField(required=False)
    calendar_event_id = serializers.IntegerField(required=False)
    sheets_entry_id = serializers.IntegerField(required=False)
    transcript_cleaned = serializers.BooleanField(required=False)
    calendar_error = serializers.CharField(required=False)

