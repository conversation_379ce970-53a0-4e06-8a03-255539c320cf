#!/usr/bin/env python3
"""
Test script for Google Sheets column ordering and calendar event creation
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_current_pii_data():
    """Check what PII data we currently have"""
    print("🔍 CHECKING CURRENT PII DATA")
    print("=" * 50)
    
    try:
        # Get the latest transcript and extract PII
        response = requests.get(f"{BASE_URL}/audio/2/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            if data['transcripts']:
                transcript_id = data['transcripts'][0]['id']
                print(f"📄 Latest transcript ID: {transcript_id}")
                
                # Extract PII
                response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                                       json={"transcript_id": transcript_id},
                                       headers={'Content-Type': 'application/json'})
                
                if response.status_code == 200:
                    pii_data = response.json().get('pii_data', {})
                    pii_id = pii_data.get('id')
                    
                    print(f"✅ PII extracted successfully - ID: {pii_id}")
                    print(f"\n📊 Current PII Data:")
                    print(f"   Names: {pii_data.get('names', [])}")
                    print(f"   Emails: {pii_data.get('emails', [])}")
                    print(f"   Dates: {pii_data.get('appointment_dates', [])}")
                    print(f"   Times: {pii_data.get('appointment_times', [])}")
                    print(f"   Addresses: {pii_data.get('addresses', [])}")
                    print(f"   Phone Numbers: {pii_data.get('phone_numbers', [])}")
                    
                    return pii_id
                else:
                    print(f"❌ PII extraction failed: {response.status_code}")
                    return None
            else:
                print("❌ No transcripts found")
                return None
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_google_sheets_save(pii_id):
    """Test saving to Google Sheets with correct column order"""
    print(f"\n📊 TESTING GOOGLE SHEETS SAVE (PII ID: {pii_id})")
    print("=" * 50)
    
    if not pii_id:
        print("❌ No PII ID available for testing")
        return
    
    try:
        response = requests.post(f"{BASE_URL}/pii/{pii_id}/save_to_sheets/")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Saved to Google Sheets successfully!")
            print(f"   Sheet entry ID: {data.get('sheet_entry_id')}")
            print(f"   Row number: {data.get('row_number')}")
            print(f"\n📋 Column Order (should be):")
            print(f"   1. Name")
            print(f"   2. Email") 
            print(f"   3. Date")
            print(f"   4. Time")
            print(f"   5. Location")
            print(f"   6. Contact")
            print(f"   7. Timestamp")
            print(f"   8. Processed")
        else:
            print(f"❌ Google Sheets save failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('error', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_calendar_event_creation(pii_id):
    """Test calendar event creation"""
    print(f"\n📅 TESTING CALENDAR EVENT CREATION (PII ID: {pii_id})")
    print("=" * 50)
    
    if not pii_id:
        print("❌ No PII ID available for testing")
        return
    
    try:
        response = requests.post(f"{BASE_URL}/calendar/create_event_from_pii/", 
                               json={"pii_data_id": pii_id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Calendar event created successfully!")
            print(f"   Event ID: {data.get('event_id')}")
            print(f"   Event Link: {data.get('event_link', 'N/A')}")
            print(f"   Name: {data.get('name', 'N/A')}")
            print(f"   Email: {data.get('email', 'N/A')}")
            print(f"   Date: {data.get('date', 'N/A')}")
            print(f"   Time: {data.get('time', 'N/A')}")
            print(f"   Location: {data.get('location', 'N/A')}")
        else:
            print(f"❌ Calendar event creation failed: {response.status_code}")
            try:
                error_data = response.json()
                error_msg = error_data.get('error', 'Unknown error')
                print(f"   Error: {error_msg}")
                
                # Provide specific guidance based on error
                if "Insufficient data" in error_msg:
                    print(f"\n💡 Troubleshooting:")
                    print(f"   - Check if names and emails are present in PII data")
                    print(f"   - Dates and times are now optional (will use defaults)")
                    print(f"   - Make sure PII extraction found customer information")
                elif "credentials" in error_msg.lower():
                    print(f"\n💡 Troubleshooting:")
                    print(f"   - Check if GOOGLE_CREDENTIALS_FILE is set")
                    print(f"   - Verify Google Calendar API is enabled")
                    print(f"   - Ensure service account has calendar permissions")
            except:
                print(f"   Raw response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_enhanced_pii_data():
    """Test with enhanced PII data that includes times"""
    print(f"\n🔧 TESTING WITH ENHANCED PII DATA")
    print("=" * 50)
    
    try:
        # First, let's create a transcript with better time information
        print("📝 Creating enhanced transcript with time information...")
        
        enhanced_transcript = """
        Hi, is this John Smith? Yes, speaking. How can I help you? 
        I'm calling regarding the house listing I saw online. Oh, great! 
        Could you share a few details with me? Sure. My name is Jane Doe 
        and my email <NAME_EMAIL>. You can also call me at 
        ************. I live at 123 Main Street, New York 10001.
        I'm available for viewing next Monday, December 5th at 2:30 PM.
        Would that work for you? Perfect! Let me schedule that for 
        December 5th at 2:30 PM. Thank you so much!
        """
        
        # For now, let's work with existing data but show what would be ideal
        print("💡 Ideal transcript should contain:")
        print("   - Clear names: 'My name is Jane Doe'")
        print("   - Clear emails: '<EMAIL>'") 
        print("   - Clear dates: 'December 5th' or '5th December'")
        print("   - Clear times: '2:30 PM' or '14:30'")
        print("   - Clear addresses: '123 Main Street, New York 10001'")
        print("   - Clear phone numbers: '************'")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 GOOGLE SHEETS & CALENDAR TESTING")
    print("=" * 60)
    print("Testing column ordering and calendar event creation")
    print()
    
    # Test current PII data
    pii_id = test_current_pii_data()
    
    # Test Google Sheets save (column ordering)
    test_google_sheets_save(pii_id)
    
    # Test calendar event creation
    test_calendar_event_creation(pii_id)
    
    # Show enhanced PII data example
    test_enhanced_pii_data()
    
    print("\n" + "=" * 60)
    print("✨ TESTING COMPLETED")
    print("=" * 60)
    print("\n📋 Summary:")
    print("• Google Sheets now saves in correct order: name, email, date, time, location, contact")
    print("• Calendar event creation is more flexible (only requires name + email)")
    print("• Missing dates/times will use defaults (today + 10:00 AM)")
    print("• Check your Google Sheets to verify column ordering")
    print("• For better calendar events, ensure PII extraction finds dates and times")
    print(f"\n🎯 Use PII ID {pii_id} for your API calls!")
