from django.contrib import admin
from .models import PIIData, GoogleSheetsEntry

@admin.register(PIIData)
class PIIDataAdmin(admin.ModelAdmin):
    list_display = ('id', 'transcript', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('names', 'phone_numbers', 'emails')

@admin.register(GoogleSheetsEntry)
class GoogleSheetsEntryAdmin(admin.ModelAdmin):
    list_display = ('id', 'pii_data', 'sheet_id', 'row_number', 'processed', 'created_at')
    list_filter = ('processed', 'created_at')
    search_fields = ('sheet_id',)

