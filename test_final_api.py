#!/usr/bin/env python3
"""
Final test of the API endpoints with the fixes
"""

import requests
import json

# API base URL
BASE_URL = "http://127.0.0.1:8000/api"

def test_complete_workflow():
    """Test the complete workflow with the fixes"""
    print("🔄 TESTING COMPLETE WORKFLOW WITH FIXES")
    print("=" * 60)
    
    audio_id = 2  # Use your audio ID
    
    try:
        # Step 1: Get current transcripts
        print("📋 Step 1: Getting current transcripts...")
        response = requests.get(f"{BASE_URL}/audio/{audio_id}/get_transcript/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {data['total_transcripts']} transcripts")
            
            if data['transcripts']:
                latest_transcript = data['transcripts'][0]  # Most recent
                transcript_id = latest_transcript['id']
                print(f"   Latest transcript ID: {transcript_id}")
                print(f"   Preview: {latest_transcript['text'][:150]}...")
            else:
                print("❌ No transcripts found")
                return
        else:
            print(f"❌ Error getting transcripts: {response.status_code}")
            return
        
        print("\n🧹 Step 2: Cleaning transcript...")
        # Clean the transcript
        response = requests.post(f"{BASE_URL}/audio/{audio_id}/clean_transcript/")
        if response.status_code == 200:
            data = response.json()
            cleaned_transcript_id = data['transcript_id']
            print(f"✅ Transcript cleaned successfully")
            print(f"   Cleaned transcript ID: {cleaned_transcript_id}")
            print(f"   Note: {data.get('note', 'N/A')}")
            print(f"   Preview: {data['cleaned_text'][:150]}...")
            
            # Check for specific improvements
            cleaned_text = data['cleaned_text']
            if "Oh," in cleaned_text:
                print("   ✅ Fixed '0' → 'Oh'")
            if "<EMAIL>" in cleaned_text.lower():
                print("   ✅ Fixed email case")
            
        else:
            print(f"❌ Error cleaning transcript: {response.status_code}")
            return
        
        print("\n🔍 Step 3: Extracting PII...")
        # Extract PII from the cleaned transcript
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": cleaned_transcript_id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            pii_data = data.get('pii_data', {})
            print(f"✅ PII extracted successfully")
            
            # Display results
            names = pii_data.get('names', [])
            emails = pii_data.get('emails', [])
            dates = pii_data.get('appointment_dates', [])
            
            print(f"\n   📊 Extraction Results:")
            print(f"     Names: {names}")
            print(f"     Emails: {emails}")
            print(f"     Dates: {dates}")
            
            # Evaluate quality
            print(f"\n   📈 Quality Assessment:")
            
            # Names evaluation
            good_names = [name for name in names if name != "Not Found" and len(name.split()) <= 2]
            if good_names:
                print(f"     ✅ Found {len(good_names)} clean names: {good_names}")
            
            bad_names = [name for name in names if name != "Not Found" and len(name.split()) > 3]
            if bad_names:
                print(f"     ❌ Found {len(bad_names)} unwanted phrases: {bad_names}")
            else:
                print(f"     ✅ No unwanted long phrases")
            
            # Email evaluation
            valid_emails = [email for email in emails if email != "Not Found" and "@" in email]
            if valid_emails:
                print(f"     ✅ Found {len(valid_emails)} valid emails: {valid_emails}")
            else:
                print(f"     ❌ No valid emails found")
            
            # Date evaluation
            valid_dates = [date for date in dates if date != "Not Found"]
            if valid_dates:
                print(f"     ✅ Found {len(valid_dates)} dates: {valid_dates}")
            else:
                print(f"     ❌ No dates found")
            
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
            print(f"   Response: {response.text}")
        
        print("\n✅ Complete workflow tested successfully!")
        
    except Exception as e:
        print(f"❌ Workflow error: {str(e)}")

def test_specific_transcript_id():
    """Test PII extraction on a specific transcript ID"""
    print("\n🎯 TESTING SPECIFIC TRANSCRIPT ID")
    print("=" * 60)
    
    transcript_id = 5  # Use the transcript ID from your example
    
    try:
        print(f"🔍 Extracting PII from transcript {transcript_id}...")
        response = requests.post(f"{BASE_URL}/pii/extract_from_transcript/", 
                               json={"transcript_id": transcript_id},
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            pii_data = data.get('pii_data', {})
            
            print(f"✅ PII extracted successfully")
            print(f"   PII Data ID: {pii_data.get('id')}")
            print(f"   Transcript ID: {pii_data.get('transcript_id')}")
            
            # Display all extracted data
            print(f"\n   📊 Complete Results:")
            for field in ['names', 'emails', 'phone_numbers', 'addresses', 'appointment_dates', 'appointment_times']:
                values = pii_data.get(field, [])
                clean_values = [v for v in values if v != "Not Found"]
                if clean_values:
                    print(f"     {field.title()}: {clean_values}")
                else:
                    print(f"     {field.title()}: None found")
            
            # Compare with expected results
            print(f"\n   🎯 Expected vs Actual:")
            
            names = pii_data.get('names', [])
            expected_names = ["John", "Jane"]
            for expected in expected_names:
                found = any(expected in name for name in names if name != "Not Found")
                status = "✅" if found else "❌"
                print(f"     {status} {expected}: {'Found' if found else 'Missing'}")
            
            emails = pii_data.get('emails', [])
            if any("<EMAIL>" in email.lower() for email in emails if email != "Not Found"):
                print(f"     ✅ Email: Found <EMAIL>")
            else:
                print(f"     ❌ Email: Missing <EMAIL>")
            
            dates = pii_data.get('appointment_dates', [])
            expected_dates = ["5th December", "7th December"]
            for expected in expected_dates:
                found = any(expected in date for date in dates if date != "Not Found")
                status = "✅" if found else "❌"
                print(f"     {status} {expected}: {'Found' if found else 'Missing'}")
            
        else:
            print(f"❌ Error extracting PII: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 FINAL API TEST WITH FIXES")
    print("=" * 60)
    print("Testing the complete workflow with all fixes applied")
    print("Make sure the Django server is running on http://127.0.0.1:8000")
    print()
    
    # Test complete workflow
    test_complete_workflow()
    
    # Test specific transcript
    test_specific_transcript_id()
    
    print("\n" + "=" * 60)
    print("✨ FINAL API TESTING COMPLETED")
    print("=" * 60)
    print("\n📋 Summary of improvements:")
    print("• ✅ Transcript cleaning: '0' → 'Oh', email case fixes")
    print("• ✅ Name extraction: Clean names only, no long phrases")
    print("• ✅ Email extraction: Proper format with case normalization")
    print("• ✅ Date extraction: Ordinal dates like '5th December'")
    print("• ✅ Better filtering: No more common words as names")
    print("\n🎯 Your PII extraction should now work much better!")
    print("Try calling your API endpoints - they should give much cleaner results!")
