#!/usr/bin/env python3
"""
Debug script to test the EnhancedTranscriptCleaner import and functionality
"""

import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'call_analysis_platform.settings')

try:
    django.setup()
    print("✅ Django setup successful")
except Exception as e:
    print(f"❌ Django setup failed: {str(e)}")
    sys.exit(1)

# Test importing the EnhancedTranscriptCleaner
try:
    from pii_extraction.enhanced_services import EnhancedTranscriptCleaner
    print("✅ EnhancedTranscriptCleaner import successful")
except Exception as e:
    print(f"❌ EnhancedTranscriptCleaner import failed: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test creating an instance
try:
    cleaner = EnhancedTranscriptCleaner()
    print("✅ EnhancedTranscriptCleaner instance created successfully")
except Exception as e:
    print(f"❌ EnhancedTranscriptCleaner instance creation failed: {str(e)}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

# Test the clean_transcript method
try:
    test_text = "Um, hello, this is john at the rate gmail dot com"
    cleaned = cleaner.clean_transcript(test_text)
    print(f"✅ clean_transcript method works")
    print(f"   Input:  {test_text}")
    print(f"   Output: {cleaned}")
except Exception as e:
    print(f"❌ clean_transcript method failed: {str(e)}")
    import traceback
    traceback.print_exc()

# Test the clean_transcript_dialogues method with a mock transcript
try:
    from audio_processing.models import Transcript, AudioFile
    
    # Check if there are any transcripts in the database
    transcripts = Transcript.objects.all()
    if transcripts.exists():
        transcript = transcripts.first()
        print(f"✅ Found transcript with ID: {transcript.id}")
        
        # Test the clean_transcript_dialogues method
        try:
            cleaned_transcript = cleaner.clean_transcript_dialogues(transcript.id)
            print(f"✅ clean_transcript_dialogues method works")
            print(f"   Transcript ID: {cleaned_transcript.id}")
            print(f"   Cleaned text preview: {cleaned_transcript.text[:100]}...")
        except Exception as e:
            print(f"❌ clean_transcript_dialogues method failed: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print("⚠️  No transcripts found in database to test clean_transcript_dialogues")
        
except Exception as e:
    print(f"❌ Database access failed: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("DEBUG COMPLETED")
print("=" * 60)
