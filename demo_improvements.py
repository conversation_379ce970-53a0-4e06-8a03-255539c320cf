#!/usr/bin/env python3
"""
Quick demonstration of the PII extraction improvements
"""

import re

def demo_email_corrections():
    """Demonstrate email correction improvements"""
    print("🔧 EMAIL CORRECTION IMPROVEMENTS")
    print("=" * 50)
    
    # Email correction patterns
    email_corrections = {
        "at the rate": "@",
        "at dred": "@", 
        "at red": "@",
        "dot com": ".com",
        "dot org": ".org"
    }
    
    email_typos = {
        "gemal": "gmail",
        "yahooo": "yahoo", 
        "gamil": "gmail",
        "hotmal": "hotmail"
    }
    
    test_cases = [
        "john dot smith at the rate gemal dot com",
        "sarah at dred yahooo dot com", 
        "mike dot jones at the rate hotmal dot org",
        "contact at gamil dot com"
    ]
    
    for test in test_cases:
        corrected = test
        
        # Apply speech corrections
        for error, fix in email_corrections.items():
            corrected = re.sub(r"\b" + re.escape(error) + r"\b", fix, corrected, flags=re.IGNORECASE)
        
        # Apply domain corrections
        for typo, correct in email_typos.items():
            corrected = re.sub(r"\b" + re.escape(typo) + r"\b", correct, corrected, flags=re.IGNORECASE)
        
        print(f"Input:  {test}")
        print(f"Output: {corrected}")
        print("-" * 30)

def demo_phone_corrections():
    """Demonstrate phone number correction improvements"""
    print("\n📞 PHONE NUMBER CORRECTION IMPROVEMENTS")
    print("=" * 50)
    
    phone_corrections = {
        "zero": "0", "one": "1", "two": "2", "three": "3", "four": "4",
        "five": "5", "six": "6", "seven": "7", "eight": "8", "nine": "9"
    }
    
    test_cases = [
        "nine one seven five five five one two three four",
        "Call me at five five five dash one two three dash four five six seven",
        "My number is area code nine one seven, five five five, one two three four"
    ]
    
    for test in test_cases:
        corrected = test
        
        # Convert spoken numbers
        for word, digit in phone_corrections.items():
            corrected = re.sub(r"\b" + re.escape(word) + r"\b", digit, corrected, flags=re.IGNORECASE)
        
        # Apply phone patterns
        corrected = re.sub(r"(\d{3})\s+(\d{3})\s+(\d{4})", r"\1\2\3", corrected)
        corrected = re.sub(r"(\d{3})\s*-\s*(\d{3})\s*-\s*(\d{4})", r"\1-\2-\3", corrected)
        
        print(f"Input:  {test}")
        print(f"Output: {corrected}")
        print("-" * 30)

def demo_address_patterns():
    """Demonstrate address pattern recognition"""
    print("\n🏠 ADDRESS PATTERN RECOGNITION")
    print("=" * 50)
    
    patterns = {
        "House Numbers": r"\b\d+\s+[\w\s]+(?:road|street|lane|avenue|st|rd|ln|ave|main|cross)\b",
        "PIN Codes": r"\b\d{6}\b",
        "Apartments": r"\b(?:flat|apartment|apt|unit)\s*[#]?\s*\d+\b",
        "Landmarks": r"\b(?:near|opp|opposite)\s+[\w\s]+(?:temple|school|hospital|mall|station)\b"
    }
    
    test_addresses = [
        "123 MG Road, Koramangala, Bengaluru 560034",
        "Flat 4B, Prestige Apartments, Brigade Road",
        "Plot 12/34, Electronic City Phase 1",
        "Near Metro Station, Whitefield Main Road"
    ]
    
    for address in test_addresses:
        print(f"Address: {address}")
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, address, re.IGNORECASE)
            if matches:
                print(f"  {pattern_name}: {matches}")
        print("-" * 30)

def demo_comprehensive_cleaning():
    """Demonstrate comprehensive transcript cleaning"""
    print("\n🧹 COMPREHENSIVE TRANSCRIPT CLEANING")
    print("=" * 50)
    
    sample_transcript = """
    Um, hello, this is, uh, John Smith calling about my appointment.
    You can reach me at john dot smith at the rate gemal dot com.
    My phone number is nine one seven, five five five, dash one two three four.
    I live at, um, one two three main street, apartment four B.
    [background noise] Sorry about that.
    """
    
    print("Original Transcript:")
    print(sample_transcript)
    
    # Apply cleaning steps
    cleaned = sample_transcript
    
    # Remove filler words
    fillers = ['um', 'uh', 'er', 'ah', 'like']
    for filler in fillers:
        cleaned = re.sub(r'\b' + re.escape(filler) + r'\b', '', cleaned, flags=re.IGNORECASE)
    
    # Remove noise patterns
    cleaned = re.sub(r'\[.*?\]', '', cleaned)
    
    # Apply email corrections
    cleaned = re.sub(r"at the rate", "@", cleaned, flags=re.IGNORECASE)
    cleaned = re.sub(r"\bgemal\b", "gmail", cleaned, flags=re.IGNORECASE)
    cleaned = re.sub(r"dot com", ".com", cleaned, flags=re.IGNORECASE)
    
    # Apply phone corrections
    phone_map = {"nine": "9", "one": "1", "seven": "7", "five": "5", "three": "3", "four": "4", "two": "2"}
    for word, digit in phone_map.items():
        cleaned = re.sub(r"\b" + word + r"\b", digit, cleaned, flags=re.IGNORECASE)
    
    # Clean up spaces
    cleaned = re.sub(r'\s+', ' ', cleaned)
    cleaned = cleaned.strip()
    
    print("\nCleaned Transcript:")
    print(cleaned)
    
    print("\nImprovements Applied:")
    print("✅ Removed filler words (um, uh)")
    print("✅ Fixed email format (at the rate → @)")
    print("✅ Corrected domain typos (gemal → gmail)")
    print("✅ Converted spoken numbers to digits")
    print("✅ Removed noise patterns [background noise]")
    print("✅ Cleaned up spacing and formatting")

if __name__ == "__main__":
    print("🚀 PII EXTRACTION IMPROVEMENTS DEMONSTRATION")
    print("=" * 60)
    
    # Demonstrate email corrections
    demo_email_corrections()
    
    # Demonstrate phone corrections
    demo_phone_corrections()
    
    # Demonstrate address patterns
    demo_address_patterns()
    
    # Demonstrate comprehensive cleaning
    demo_comprehensive_cleaning()
    
    print("\n" + "=" * 60)
    print("✨ DEMONSTRATION COMPLETED")
    print("=" * 60)
    print("\n📋 Key Improvements Summary:")
    print("• Enhanced email format correction with speech-to-text fixes")
    print("• Comprehensive phone number pattern recognition")
    print("• Advanced address extraction with Indian format support")
    print("• Intelligent transcript cleaning with filler word removal")
    print("• Groq LLM integration for context-aware PII extraction")
    print("• IndicNER model for Indian language and location recognition")
    print("• Robust fallback mechanisms and error handling")
    print("\n🎯 Ready for production use with improved accuracy and reliability!")
