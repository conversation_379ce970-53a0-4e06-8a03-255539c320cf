# Generated by Django 5.2.2 on 2025-06-07 16:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('pii_extraction', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CalendarEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('calendar_id', models.Char<PERSON>ield(max_length=255)),
                ('event_id', models.Char<PERSON>ield(max_length=255)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('email', models.EmailField(max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('date', models.DateField()),
                ('time', models.TimeField()),
                ('location', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('event_link', models.URLField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pii_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to='pii_extraction.piidata')),
            ],
        ),
        migrations.CreateModel(
            name='EmailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(max_length=50)),
                ('calendar_event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='email_notifications', to='calendar_scheduler.calendarevent')),
            ],
        ),
        migrations.CreateModel(
            name='SMSNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(max_length=50)),
                ('calendar_event', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sms_notifications', to='calendar_scheduler.calendarevent')),
            ],
        ),
    ]
